{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Choreo/frontend/src/lib/auth.ts"], "sourcesContent": ["/**\n * Authentication utilities for Choreo managed authentication\n * Handles login, logout, user info retrieval, and session management\n */\n\nimport Cookies from 'js-cookie'\nimport { User } from '@/types'\n\n/**\n * Authentication service for Choreo managed authentication\n */\nexport class AuthService {\n  private static instance: AuthService\n  private user: User | null = null\n  private isAuthenticated: boolean = false\n\n  private constructor() {}\n\n  static getInstance(): AuthService {\n    if (!AuthService.instance) {\n      AuthService.instance = new AuthService()\n    }\n    return AuthService.instance\n  }\n\n  /**\n   * Initiate login by redirecting to Choreo auth endpoint\n   * @param redirectPath - Optional path to redirect to after login\n   */\n  login(redirectPath?: string): void {\n    const loginUrl = '/auth/login'\n    const url = redirectPath ? `${loginUrl}?redirect=${encodeURIComponent(redirectPath)}` : loginUrl\n    window.location.href = url\n  }\n\n  /**\n   * Initiate logout by redirecting to Choreo auth endpoint\n   */\n  logout(): void {\n    const sessionHint = Cookies.get('session_hint')\n    const logoutUrl = sessionHint \n      ? `/auth/logout?session_hint=${sessionHint}`\n      : '/auth/logout'\n    \n    // Clear local user state\n    this.user = null\n    this.isAuthenticated = false\n    \n    // Clear any stored user data\n    this.clearStoredUserData()\n    \n    window.location.href = logoutUrl\n  }\n\n  /**\n   * Get user information from userinfo cookie (available after login)\n   * This should be called on the post-login page\n   */\n  getUserInfoFromCookie(): User | null {\n    try {\n      const encodedUserInfo = Cookies.get('userinfo')\n      \n      if (!encodedUserInfo) {\n        return null\n      }\n\n      // Decode the base64 encoded user info\n      const userInfo = JSON.parse(atob(encodedUserInfo))\n      \n      // Clear the cookie after reading (as recommended by Choreo)\n      Cookies.remove('userinfo', { path: '/' })\n      \n      // Store user info for session\n      this.user = {\n        id: userInfo.sub,\n        email: userInfo.email,\n        name: userInfo.name || `${userInfo.given_name || ''} ${userInfo.family_name || ''}`.trim(),\n        username: userInfo.preferred_username,\n        groups: userInfo.groups || [],\n        roles: userInfo.roles || [],\n        profileComplete: !!(userInfo.name && userInfo.email)\n      }\n      \n      this.isAuthenticated = true\n      \n      // Store in localStorage for session persistence\n      this.storeUserData(this.user)\n      \n      return this.user\n    } catch (error) {\n      console.error('Error parsing user info cookie:', error)\n      return null\n    }\n  }\n\n  /**\n   * Check authentication status by calling the userinfo endpoint\n   */\n  async checkAuthStatus(): Promise<{ isAuthenticated: boolean; user: User | null }> {\n    try {\n      const response = await fetch('/auth/userinfo', {\n        method: 'GET',\n        credentials: 'include',\n        headers: {\n          'Accept': 'application/json',\n        }\n      })\n\n      if (response.ok) {\n        const userInfo = await response.json()\n        \n        this.user = {\n          id: userInfo.sub,\n          email: userInfo.email,\n          name: userInfo.name || `${userInfo.given_name || ''} ${userInfo.family_name || ''}`.trim(),\n          username: userInfo.preferred_username,\n          groups: userInfo.groups || [],\n          roles: userInfo.roles || [],\n          profileComplete: !!(userInfo.name && userInfo.email)\n        }\n        \n        this.isAuthenticated = true\n        this.storeUserData(this.user)\n        \n        return { isAuthenticated: true, user: this.user }\n      } else if (response.status === 401) {\n        // User is not authenticated\n        this.user = null\n        this.isAuthenticated = false\n        this.clearStoredUserData()\n        \n        return { isAuthenticated: false, user: null }\n      } else {\n        throw new Error(`Auth check failed with status: ${response.status}`)\n      }\n    } catch (error) {\n      console.error('Error checking auth status:', error)\n      \n      // Try to get user from localStorage as fallback\n      const storedUser = this.getStoredUserData()\n      if (storedUser) {\n        this.user = storedUser\n        this.isAuthenticated = true\n        return { isAuthenticated: true, user: storedUser }\n      }\n      \n      return { isAuthenticated: false, user: null }\n    }\n  }\n\n  /**\n   * Get current user (from memory or localStorage)\n   */\n  getCurrentUser(): User | null {\n    if (this.user) {\n      return this.user\n    }\n    \n    // Try to get from localStorage\n    const storedUser = this.getStoredUserData()\n    if (storedUser) {\n      this.user = storedUser\n      this.isAuthenticated = true\n      return storedUser\n    }\n    \n    return null\n  }\n\n  /**\n   * Check if user is currently authenticated\n   */\n  getIsAuthenticated(): boolean {\n    return this.isAuthenticated || !!this.getStoredUserData()\n  }\n\n  /**\n   * Handle session expiry (401 responses from API calls)\n   * Automatically redirect to login\n   */\n  handleSessionExpiry(): void {\n    console.log('Session expired, redirecting to login...')\n    this.user = null\n    this.isAuthenticated = false\n    this.clearStoredUserData()\n    this.login(window.location.pathname)\n  }\n\n  /**\n   * Store user data in localStorage\n   */\n  private storeUserData(user: User): void {\n    try {\n      localStorage.setItem('user', JSON.stringify(user))\n      localStorage.setItem('lastLogin', new Date().toISOString())\n    } catch (error) {\n      console.error('Error storing user data:', error)\n    }\n  }\n\n  /**\n   * Get stored user data from localStorage\n   */\n  private getStoredUserData(): User | null {\n    try {\n      const userData = localStorage.getItem('user')\n      if (userData) {\n        return JSON.parse(userData)\n      }\n    } catch (error) {\n      console.error('Error retrieving stored user data:', error)\n    }\n    return null\n  }\n\n  /**\n   * Clear stored user data\n   */\n  private clearStoredUserData(): void {\n    try {\n      localStorage.removeItem('user')\n      localStorage.removeItem('lastLogin')\n    } catch (error) {\n      console.error('Error clearing stored user data:', error)\n    }\n  }\n}\n\n// Export singleton instance\nexport const authService = AuthService.getInstance()\n\n// Utility functions\nexport const isAuthenticated = (): boolean => authService.getIsAuthenticated()\nexport const getCurrentUser = (): User | null => authService.getCurrentUser()\nexport const login = (redirectPath?: string): void => authService.login(redirectPath)\nexport const logout = (): void => authService.logout()\nexport const handleSessionExpiry = (): void => authService.handleSessionExpiry()\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;AAED;;AAMO,MAAM;IACX,OAAe,SAAqB;IAC5B,OAAoB,KAAI;IACxB,kBAA2B,MAAK;IAExC,aAAsB,CAAC;IAEvB,OAAO,cAA2B;QAChC,IAAI,CAAC,YAAY,QAAQ,EAAE;YACzB,YAAY,QAAQ,GAAG,IAAI;QAC7B;QACA,OAAO,YAAY,QAAQ;IAC7B;IAEA;;;GAGC,GACD,MAAM,YAAqB,EAAQ;QACjC,MAAM,WAAW;QACjB,MAAM,MAAM,eAAe,GAAG,SAAS,UAAU,EAAE,mBAAmB,eAAe,GAAG;QACxF,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA;;GAEC,GACD,SAAe;QACb,MAAM,cAAc,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;QAChC,MAAM,YAAY,cACd,CAAC,0BAA0B,EAAE,aAAa,GAC1C;QAEJ,yBAAyB;QACzB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,eAAe,GAAG;QAEvB,6BAA6B;QAC7B,IAAI,CAAC,mBAAmB;QAExB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA;;;GAGC,GACD,wBAAqC;QACnC,IAAI;YACF,MAAM,kBAAkB,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;YAEpC,IAAI,CAAC,iBAAiB;gBACpB,OAAO;YACT;YAEA,sCAAsC;YACtC,MAAM,WAAW,KAAK,KAAK,CAAC,KAAK;YAEjC,4DAA4D;YAC5D,wJAAA,CAAA,UAAO,CAAC,MAAM,CAAC,YAAY;gBAAE,MAAM;YAAI;YAEvC,8BAA8B;YAC9B,IAAI,CAAC,IAAI,GAAG;gBACV,IAAI,SAAS,GAAG;gBAChB,OAAO,SAAS,KAAK;gBACrB,MAAM,SAAS,IAAI,IAAI,GAAG,SAAS,UAAU,IAAI,GAAG,CAAC,EAAE,SAAS,WAAW,IAAI,IAAI,CAAC,IAAI;gBACxF,UAAU,SAAS,kBAAkB;gBACrC,QAAQ,SAAS,MAAM,IAAI,EAAE;gBAC7B,OAAO,SAAS,KAAK,IAAI,EAAE;gBAC3B,iBAAiB,CAAC,CAAC,CAAC,SAAS,IAAI,IAAI,SAAS,KAAK;YACrD;YAEA,IAAI,CAAC,eAAe,GAAG;YAEvB,gDAAgD;YAChD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI;YAE5B,OAAO,IAAI,CAAC,IAAI;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO;QACT;IACF;IAEA;;GAEC,GACD,MAAM,kBAA4E;QAChF,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,kBAAkB;gBAC7C,QAAQ;gBACR,aAAa;gBACb,SAAS;oBACP,UAAU;gBACZ;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,WAAW,MAAM,SAAS,IAAI;gBAEpC,IAAI,CAAC,IAAI,GAAG;oBACV,IAAI,SAAS,GAAG;oBAChB,OAAO,SAAS,KAAK;oBACrB,MAAM,SAAS,IAAI,IAAI,GAAG,SAAS,UAAU,IAAI,GAAG,CAAC,EAAE,SAAS,WAAW,IAAI,IAAI,CAAC,IAAI;oBACxF,UAAU,SAAS,kBAAkB;oBACrC,QAAQ,SAAS,MAAM,IAAI,EAAE;oBAC7B,OAAO,SAAS,KAAK,IAAI,EAAE;oBAC3B,iBAAiB,CAAC,CAAC,CAAC,SAAS,IAAI,IAAI,SAAS,KAAK;gBACrD;gBAEA,IAAI,CAAC,eAAe,GAAG;gBACvB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI;gBAE5B,OAAO;oBAAE,iBAAiB;oBAAM,MAAM,IAAI,CAAC,IAAI;gBAAC;YAClD,OAAO,IAAI,SAAS,MAAM,KAAK,KAAK;gBAClC,4BAA4B;gBAC5B,IAAI,CAAC,IAAI,GAAG;gBACZ,IAAI,CAAC,eAAe,GAAG;gBACvB,IAAI,CAAC,mBAAmB;gBAExB,OAAO;oBAAE,iBAAiB;oBAAO,MAAM;gBAAK;YAC9C,OAAO;gBACL,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,SAAS,MAAM,EAAE;YACrE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAE7C,gDAAgD;YAChD,MAAM,aAAa,IAAI,CAAC,iBAAiB;YACzC,IAAI,YAAY;gBACd,IAAI,CAAC,IAAI,GAAG;gBACZ,IAAI,CAAC,eAAe,GAAG;gBACvB,OAAO;oBAAE,iBAAiB;oBAAM,MAAM;gBAAW;YACnD;YAEA,OAAO;gBAAE,iBAAiB;gBAAO,MAAM;YAAK;QAC9C;IACF;IAEA;;GAEC,GACD,iBAA8B;QAC5B,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,OAAO,IAAI,CAAC,IAAI;QAClB;QAEA,+BAA+B;QAC/B,MAAM,aAAa,IAAI,CAAC,iBAAiB;QACzC,IAAI,YAAY;YACd,IAAI,CAAC,IAAI,GAAG;YACZ,IAAI,CAAC,eAAe,GAAG;YACvB,OAAO;QACT;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,qBAA8B;QAC5B,OAAO,IAAI,CAAC,eAAe,IAAI,CAAC,CAAC,IAAI,CAAC,iBAAiB;IACzD;IAEA;;;GAGC,GACD,sBAA4B;QAC1B,QAAQ,GAAG,CAAC;QACZ,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,mBAAmB;QACxB,IAAI,CAAC,KAAK,CAAC,OAAO,QAAQ,CAAC,QAAQ;IACrC;IAEA;;GAEC,GACD,AAAQ,cAAc,IAAU,EAAQ;QACtC,IAAI;YACF,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;YAC5C,aAAa,OAAO,CAAC,aAAa,IAAI,OAAO,WAAW;QAC1D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA;;GAEC,GACD,AAAQ,oBAAiC;QACvC,IAAI;YACF,MAAM,WAAW,aAAa,OAAO,CAAC;YACtC,IAAI,UAAU;gBACZ,OAAO,KAAK,KAAK,CAAC;YACpB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;QACtD;QACA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,sBAA4B;QAClC,IAAI;YACF,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD;IACF;AACF;AAGO,MAAM,cAAc,YAAY,WAAW;AAG3C,MAAM,kBAAkB,IAAe,YAAY,kBAAkB;AACrE,MAAM,iBAAiB,IAAmB,YAAY,cAAc;AACpE,MAAM,QAAQ,CAAC,eAAgC,YAAY,KAAK,CAAC;AACjE,MAAM,SAAS,IAAY,YAAY,MAAM;AAC7C,MAAM,sBAAsB,IAAY,YAAY,mBAAmB", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Choreo/frontend/src/components/LoadingSpinner.tsx"], "sourcesContent": ["'use client'\n\ninterface LoadingSpinnerProps {\n  size?: 'small' | 'medium' | 'large'\n  className?: string\n  text?: string\n}\n\nexport default function LoadingSpinner({ \n  size = 'medium', \n  className = '', \n  text \n}: LoadingSpinnerProps) {\n  const sizeClasses = {\n    small: 'w-4 h-4',\n    medium: 'w-6 h-6',\n    large: 'w-8 h-8'\n  }\n\n  return (\n    <div className={`flex flex-col items-center justify-center ${className}`}>\n      <div className={`spinner ${sizeClasses[size]}`}></div>\n      {text && (\n        <p className=\"mt-2 text-sm text-secondary-600\">{text}</p>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAQe,SAAS,eAAe,EACrC,OAAO,QAAQ,EACf,YAAY,EAAE,EACd,IAAI,EACgB;IACpB,MAAM,cAAc;QAClB,OAAO;QACP,QAAQ;QACR,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,0CAA0C,EAAE,WAAW;;0BACtE,6LAAC;gBAAI,WAAW,CAAC,QAAQ,EAAE,WAAW,CAAC,KAAK,EAAE;;;;;;YAC7C,sBACC,6LAAC;gBAAE,WAAU;0BAAmC;;;;;;;;;;;;AAIxD;KAnBwB", "debugId": null}}, {"offset": {"line": 272, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Choreo/frontend/src/app/dashboard/layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { authService } from '@/lib/auth'\nimport LoadingSpinner from '@/components/LoadingSpinner'\n\nexport default function DashboardLayout({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  const [isLoading, setIsLoading] = useState(true)\n  const [isAuthenticated, setIsAuthenticated] = useState(false)\n  const router = useRouter()\n\n  useEffect(() => {\n    checkAuthentication()\n  }, [])\n\n  const checkAuthentication = async () => {\n    try {\n      // First, try to get user info from cookie (post-login)\n      const userFromCookie = authService.getUserInfoFromCookie()\n      \n      if (userFromCookie) {\n        setIsAuthenticated(true)\n        setIsLoading(false)\n        return\n      }\n\n      // If no cookie, check auth status via API\n      const authResult = await authService.checkAuthStatus()\n      \n      if (authResult.isAuthenticated) {\n        setIsAuthenticated(true)\n      } else {\n        // Redirect to login if not authenticated\n        authService.login('/dashboard')\n        return\n      }\n    } catch (error) {\n      console.error('Authentication check failed:', error)\n      // Redirect to login on error\n      authService.login('/dashboard')\n      return\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-secondary-50\">\n        <LoadingSpinner size=\"large\" text=\"Checking authentication...\" />\n      </div>\n    )\n  }\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-secondary-50\">\n        <LoadingSpinner size=\"large\" text=\"Redirecting to login...\" />\n      </div>\n    )\n  }\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS,gBAAgB,EACtC,QAAQ,EAGT;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;QACF;oCAAG,EAAE;IAEL,MAAM,sBAAsB;QAC1B,IAAI;YACF,uDAAuD;YACvD,MAAM,iBAAiB,qHAAA,CAAA,cAAW,CAAC,qBAAqB;YAExD,IAAI,gBAAgB;gBAClB,mBAAmB;gBACnB,aAAa;gBACb;YACF;YAEA,0CAA0C;YAC1C,MAAM,aAAa,MAAM,qHAAA,CAAA,cAAW,CAAC,eAAe;YAEpD,IAAI,WAAW,eAAe,EAAE;gBAC9B,mBAAmB;YACrB,OAAO;gBACL,yCAAyC;gBACzC,qHAAA,CAAA,cAAW,CAAC,KAAK,CAAC;gBAClB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,6BAA6B;YAC7B,qHAAA,CAAA,cAAW,CAAC,KAAK,CAAC;YAClB;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,uIAAA,CAAA,UAAc;gBAAC,MAAK;gBAAQ,MAAK;;;;;;;;;;;IAGxC;IAEA,IAAI,CAAC,iBAAiB;QACpB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,uIAAA,CAAA,UAAc;gBAAC,MAAK;gBAAQ,MAAK;;;;;;;;;;;IAGxC;IAEA,qBAAO;kBAAG;;AACZ;GA7DwB;;QAOP,qIAAA,CAAA,YAAS;;;KAPF", "debugId": null}}, {"offset": {"line": 378, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Choreo/frontend/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;YAC3C,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,4BAA4B,SAAU,iBAAiB;YACrD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,KAAK,CAAC,2BAA2B,CAAC,IAAI,CACjE,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 586, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Choreo/frontend/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 598, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Choreo/frontend/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 605, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Choreo/frontend/node_modules/js-cookie/dist/js.cookie.mjs"], "sourcesContent": ["/*! js-cookie v3.0.5 | MIT */\n/* eslint-disable no-var */\nfunction assign (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n    for (var key in source) {\n      target[key] = source[key];\n    }\n  }\n  return target\n}\n/* eslint-enable no-var */\n\n/* eslint-disable no-var */\nvar defaultConverter = {\n  read: function (value) {\n    if (value[0] === '\"') {\n      value = value.slice(1, -1);\n    }\n    return value.replace(/(%[\\dA-F]{2})+/gi, decodeURIComponent)\n  },\n  write: function (value) {\n    return encodeURIComponent(value).replace(\n      /%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,\n      decodeURIComponent\n    )\n  }\n};\n/* eslint-enable no-var */\n\n/* eslint-disable no-var */\n\nfunction init (converter, defaultAttributes) {\n  function set (name, value, attributes) {\n    if (typeof document === 'undefined') {\n      return\n    }\n\n    attributes = assign({}, defaultAttributes, attributes);\n\n    if (typeof attributes.expires === 'number') {\n      attributes.expires = new Date(Date.now() + attributes.expires * 864e5);\n    }\n    if (attributes.expires) {\n      attributes.expires = attributes.expires.toUTCString();\n    }\n\n    name = encodeURIComponent(name)\n      .replace(/%(2[346B]|5E|60|7C)/g, decodeURIComponent)\n      .replace(/[()]/g, escape);\n\n    var stringifiedAttributes = '';\n    for (var attributeName in attributes) {\n      if (!attributes[attributeName]) {\n        continue\n      }\n\n      stringifiedAttributes += '; ' + attributeName;\n\n      if (attributes[attributeName] === true) {\n        continue\n      }\n\n      // Considers RFC 6265 section 5.2:\n      // ...\n      // 3.  If the remaining unparsed-attributes contains a %x3B (\";\")\n      //     character:\n      // Consume the characters of the unparsed-attributes up to,\n      // not including, the first %x3B (\";\") character.\n      // ...\n      stringifiedAttributes += '=' + attributes[attributeName].split(';')[0];\n    }\n\n    return (document.cookie =\n      name + '=' + converter.write(value, name) + stringifiedAttributes)\n  }\n\n  function get (name) {\n    if (typeof document === 'undefined' || (arguments.length && !name)) {\n      return\n    }\n\n    // To prevent the for loop in the first place assign an empty array\n    // in case there are no cookies at all.\n    var cookies = document.cookie ? document.cookie.split('; ') : [];\n    var jar = {};\n    for (var i = 0; i < cookies.length; i++) {\n      var parts = cookies[i].split('=');\n      var value = parts.slice(1).join('=');\n\n      try {\n        var found = decodeURIComponent(parts[0]);\n        jar[found] = converter.read(value, found);\n\n        if (name === found) {\n          break\n        }\n      } catch (e) {}\n    }\n\n    return name ? jar[name] : jar\n  }\n\n  return Object.create(\n    {\n      set,\n      get,\n      remove: function (name, attributes) {\n        set(\n          name,\n          '',\n          assign({}, attributes, {\n            expires: -1\n          })\n        );\n      },\n      withAttributes: function (attributes) {\n        return init(this.converter, assign({}, this.attributes, attributes))\n      },\n      withConverter: function (converter) {\n        return init(assign({}, this.converter, converter), this.attributes)\n      }\n    },\n    {\n      attributes: { value: Object.freeze(defaultAttributes) },\n      converter: { value: Object.freeze(converter) }\n    }\n  )\n}\n\nvar api = init(defaultConverter, { path: '/' });\n/* eslint-enable no-var */\n\nexport { api as default };\n"], "names": [], "mappings": "AAAA,2BAA2B,GAC3B,yBAAyB;;;AACzB,SAAS,OAAQ,MAAM;IACrB,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QACzC,IAAI,SAAS,SAAS,CAAC,EAAE;QACzB,IAAK,IAAI,OAAO,OAAQ;YACtB,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;QAC3B;IACF;IACA,OAAO;AACT;AACA,wBAAwB,GAExB,yBAAyB,GACzB,IAAI,mBAAmB;IACrB,MAAM,SAAU,KAAK;QACnB,IAAI,KAAK,CAAC,EAAE,KAAK,KAAK;YACpB,QAAQ,MAAM,KAAK,CAAC,GAAG,CAAC;QAC1B;QACA,OAAO,MAAM,OAAO,CAAC,oBAAoB;IAC3C;IACA,OAAO,SAAU,KAAK;QACpB,OAAO,mBAAmB,OAAO,OAAO,CACtC,4CACA;IAEJ;AACF;AACA,wBAAwB,GAExB,yBAAyB,GAEzB,SAAS,KAAM,SAAS,EAAE,iBAAiB;IACzC,SAAS,IAAK,IAAI,EAAE,KAAK,EAAE,UAAU;QACnC,IAAI,OAAO,aAAa,aAAa;YACnC;QACF;QAEA,aAAa,OAAO,CAAC,GAAG,mBAAmB;QAE3C,IAAI,OAAO,WAAW,OAAO,KAAK,UAAU;YAC1C,WAAW,OAAO,GAAG,IAAI,KAAK,KAAK,GAAG,KAAK,WAAW,OAAO,GAAG;QAClE;QACA,IAAI,WAAW,OAAO,EAAE;YACtB,WAAW,OAAO,GAAG,WAAW,OAAO,CAAC,WAAW;QACrD;QAEA,OAAO,mBAAmB,MACvB,OAAO,CAAC,wBAAwB,oBAChC,OAAO,CAAC,SAAS;QAEpB,IAAI,wBAAwB;QAC5B,IAAK,IAAI,iBAAiB,WAAY;YACpC,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE;gBAC9B;YACF;YAEA,yBAAyB,OAAO;YAEhC,IAAI,UAAU,CAAC,cAAc,KAAK,MAAM;gBACtC;YACF;YAEA,kCAAkC;YAClC,MAAM;YACN,iEAAiE;YACjE,iBAAiB;YACjB,2DAA2D;YAC3D,iDAAiD;YACjD,MAAM;YACN,yBAAyB,MAAM,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QACxE;QAEA,OAAQ,SAAS,MAAM,GACrB,OAAO,MAAM,UAAU,KAAK,CAAC,OAAO,QAAQ;IAChD;IAEA,SAAS,IAAK,IAAI;QAChB,IAAI,OAAO,aAAa,eAAgB,UAAU,MAAM,IAAI,CAAC,MAAO;YAClE;QACF;QAEA,mEAAmE;QACnE,uCAAuC;QACvC,IAAI,UAAU,SAAS,MAAM,GAAG,SAAS,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE;QAChE,IAAI,MAAM,CAAC;QACX,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACvC,IAAI,QAAQ,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC;YAC7B,IAAI,QAAQ,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC;YAEhC,IAAI;gBACF,IAAI,QAAQ,mBAAmB,KAAK,CAAC,EAAE;gBACvC,GAAG,CAAC,MAAM,GAAG,UAAU,IAAI,CAAC,OAAO;gBAEnC,IAAI,SAAS,OAAO;oBAClB;gBACF;YACF,EAAE,OAAO,GAAG,CAAC;QACf;QAEA,OAAO,OAAO,GAAG,CAAC,KAAK,GAAG;IAC5B;IAEA,OAAO,OAAO,MAAM,CAClB;QACE;QACA;QACA,QAAQ,SAAU,IAAI,EAAE,UAAU;YAChC,IACE,MACA,IACA,OAAO,CAAC,GAAG,YAAY;gBACrB,SAAS,CAAC;YACZ;QAEJ;QACA,gBAAgB,SAAU,UAAU;YAClC,OAAO,KAAK,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE;QAC1D;QACA,eAAe,SAAU,SAAS;YAChC,OAAO,KAAK,OAAO,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,YAAY,IAAI,CAAC,UAAU;QACpE;IACF,GACA;QACE,YAAY;YAAE,OAAO,OAAO,MAAM,CAAC;QAAmB;QACtD,WAAW;YAAE,OAAO,OAAO,MAAM,CAAC;QAAW;IAC/C;AAEJ;AAEA,IAAI,MAAM,KAAK,kBAAkB;IAAE,MAAM;AAAI", "ignoreList": [0], "debugId": null}}]}