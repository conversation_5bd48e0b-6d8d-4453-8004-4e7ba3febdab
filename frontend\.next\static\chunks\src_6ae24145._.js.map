{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Choreo/frontend/src/components/ErrorMessage.tsx"], "sourcesContent": ["'use client'\n\nimport { AlertCircle, X } from 'lucide-react'\n\ninterface ErrorMessageProps {\n  message: string\n  onDismiss?: () => void\n  className?: string\n  variant?: 'error' | 'warning' | 'info'\n}\n\nexport default function ErrorMessage({ \n  message, \n  onDismiss, \n  className = '',\n  variant = 'error'\n}: ErrorMessageProps) {\n  const variantClasses = {\n    error: 'bg-danger-50 border-danger-200 text-danger-800',\n    warning: 'bg-warning-50 border-warning-200 text-warning-800',\n    info: 'bg-primary-50 border-primary-200 text-primary-800'\n  }\n\n  const iconClasses = {\n    error: 'text-danger-600',\n    warning: 'text-warning-600',\n    info: 'text-primary-600'\n  }\n\n  return (\n    <div className={`rounded-lg border p-4 ${variantClasses[variant]} ${className}`}>\n      <div className=\"flex items-start\">\n        <AlertCircle className={`w-5 h-5 mt-0.5 mr-3 flex-shrink-0 ${iconClasses[variant]}`} />\n        <div className=\"flex-1\">\n          <p className=\"text-sm font-medium\">{message}</p>\n        </div>\n        {onDismiss && (\n          <button\n            onClick={onDismiss}\n            className={`ml-3 flex-shrink-0 p-1 rounded-md hover:bg-opacity-20 hover:bg-current transition-colors duration-200 ${iconClasses[variant]}`}\n          >\n            <X className=\"w-4 h-4\" />\n          </button>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AAWe,SAAS,aAAa,EACnC,OAAO,EACP,SAAS,EACT,YAAY,EAAE,EACd,UAAU,OAAO,EACC;IAClB,MAAM,iBAAiB;QACrB,OAAO;QACP,SAAS;QACT,MAAM;IACR;IAEA,MAAM,cAAc;QAClB,OAAO;QACP,SAAS;QACT,MAAM;IACR;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,sBAAsB,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC,EAAE,WAAW;kBAC7E,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAW,CAAC,kCAAkC,EAAE,WAAW,CAAC,QAAQ,EAAE;;;;;;8BACnF,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAuB;;;;;;;;;;;gBAErC,2BACC,6LAAC;oBACC,SAAS;oBACT,WAAW,CAAC,sGAAsG,EAAE,WAAW,CAAC,QAAQ,EAAE;8BAE1I,cAAA,6LAAC,+LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMzB;KApCwB", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Choreo/frontend/src/components/FilterBar.tsx"], "sourcesContent": ["'use client'\n\nimport { Filter, SortAsc, SortDesc, X } from 'lucide-react'\nimport { TaskFilters, TaskStats } from '@/types'\n\ninterface FilterBarProps {\n  filters: TaskFilters\n  onFiltersChange: (filters: TaskFilters) => void\n  taskStats: TaskStats\n}\n\nexport default function FilterBar({ filters, onFiltersChange, taskStats }: FilterBarProps) {\n  const handleFilterChange = (key: keyof TaskFilters, value: string | undefined) => {\n    const newFilters = { ...filters }\n    \n    if (value === '' || value === undefined) {\n      delete newFilters[key]\n    } else {\n      newFilters[key] = value as any\n    }\n    \n    onFiltersChange(newFilters)\n  }\n\n  const clearAllFilters = () => {\n    onFiltersChange({})\n  }\n\n  const hasActiveFilters = Object.keys(filters).length > 0\n\n  return (\n    <div className=\"flex flex-wrap items-center gap-4\">\n      {/* Status Filter */}\n      <div className=\"flex items-center space-x-2\">\n        <label htmlFor=\"status-filter\" className=\"text-sm font-medium text-secondary-700\">\n          Status:\n        </label>\n        <select\n          id=\"status-filter\"\n          value={filters.status || ''}\n          onChange={(e) => handleFilterChange('status', e.target.value)}\n          className=\"text-sm border-secondary-300 rounded-md focus:border-primary-500 focus:ring-primary-500\"\n        >\n          <option value=\"\">All ({taskStats.total})</option>\n          <option value=\"todo\">To Do ({taskStats.byStatus.todo})</option>\n          <option value=\"in-progress\">In Progress ({taskStats.byStatus['in-progress']})</option>\n          <option value=\"completed\">Completed ({taskStats.byStatus.completed})</option>\n        </select>\n      </div>\n\n      {/* Priority Filter */}\n      <div className=\"flex items-center space-x-2\">\n        <label htmlFor=\"priority-filter\" className=\"text-sm font-medium text-secondary-700\">\n          Priority:\n        </label>\n        <select\n          id=\"priority-filter\"\n          value={filters.priority || ''}\n          onChange={(e) => handleFilterChange('priority', e.target.value)}\n          className=\"text-sm border-secondary-300 rounded-md focus:border-primary-500 focus:ring-primary-500\"\n        >\n          <option value=\"\">All</option>\n          <option value=\"high\">High ({taskStats.byPriority.high})</option>\n          <option value=\"medium\">Medium ({taskStats.byPriority.medium})</option>\n          <option value=\"low\">Low ({taskStats.byPriority.low})</option>\n        </select>\n      </div>\n\n      {/* Sort By */}\n      <div className=\"flex items-center space-x-2\">\n        <label htmlFor=\"sort-filter\" className=\"text-sm font-medium text-secondary-700\">\n          Sort by:\n        </label>\n        <select\n          id=\"sort-filter\"\n          value={filters.sortBy || 'createdAt'}\n          onChange={(e) => handleFilterChange('sortBy', e.target.value)}\n          className=\"text-sm border-secondary-300 rounded-md focus:border-primary-500 focus:ring-primary-500\"\n        >\n          <option value=\"createdAt\">Created Date</option>\n          <option value=\"updatedAt\">Updated Date</option>\n          <option value=\"title\">Title</option>\n          <option value=\"priority\">Priority</option>\n          <option value=\"dueDate\">Due Date</option>\n        </select>\n      </div>\n\n      {/* Sort Order */}\n      <button\n        onClick={() => handleFilterChange('sortOrder', filters.sortOrder === 'desc' ? 'asc' : 'desc')}\n        className=\"flex items-center space-x-1 px-3 py-1 text-sm border border-secondary-300 rounded-md hover:bg-secondary-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500\"\n        title={`Sort ${filters.sortOrder === 'desc' ? 'ascending' : 'descending'}`}\n      >\n        {filters.sortOrder === 'desc' ? (\n          <SortDesc className=\"w-4 h-4\" />\n        ) : (\n          <SortAsc className=\"w-4 h-4\" />\n        )}\n        <span>{filters.sortOrder === 'desc' ? 'Desc' : 'Asc'}</span>\n      </button>\n\n      {/* Clear Filters */}\n      {hasActiveFilters && (\n        <button\n          onClick={clearAllFilters}\n          className=\"flex items-center space-x-1 px-3 py-1 text-sm text-danger-600 border border-danger-300 rounded-md hover:bg-danger-50 focus:outline-none focus:ring-2 focus:ring-danger-500 focus:border-danger-500\"\n        >\n          <X className=\"w-4 h-4\" />\n          <span>Clear</span>\n        </button>\n      )}\n\n      {/* Active Filters Indicator */}\n      {hasActiveFilters && (\n        <div className=\"flex items-center space-x-1 text-sm text-secondary-600\">\n          <Filter className=\"w-4 h-4\" />\n          <span>{Object.keys(filters).length} filter{Object.keys(filters).length !== 1 ? 's' : ''} active</span>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAFA;;;AAWe,SAAS,UAAU,EAAE,OAAO,EAAE,eAAe,EAAE,SAAS,EAAkB;IACvF,MAAM,qBAAqB,CAAC,KAAwB;QAClD,MAAM,aAAa;YAAE,GAAG,OAAO;QAAC;QAEhC,IAAI,UAAU,MAAM,UAAU,WAAW;YACvC,OAAO,UAAU,CAAC,IAAI;QACxB,OAAO;YACL,UAAU,CAAC,IAAI,GAAG;QACpB;QAEA,gBAAgB;IAClB;IAEA,MAAM,kBAAkB;QACtB,gBAAgB,CAAC;IACnB;IAEA,MAAM,mBAAmB,OAAO,IAAI,CAAC,SAAS,MAAM,GAAG;IAEvD,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,SAAQ;wBAAgB,WAAU;kCAAyC;;;;;;kCAGlF,6LAAC;wBACC,IAAG;wBACH,OAAO,QAAQ,MAAM,IAAI;wBACzB,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK;wBAC5D,WAAU;;0CAEV,6LAAC;gCAAO,OAAM;;oCAAG;oCAAM,UAAU,KAAK;oCAAC;;;;;;;0CACvC,6LAAC;gCAAO,OAAM;;oCAAO;oCAAQ,UAAU,QAAQ,CAAC,IAAI;oCAAC;;;;;;;0CACrD,6LAAC;gCAAO,OAAM;;oCAAc;oCAAc,UAAU,QAAQ,CAAC,cAAc;oCAAC;;;;;;;0CAC5E,6LAAC;gCAAO,OAAM;;oCAAY;oCAAY,UAAU,QAAQ,CAAC,SAAS;oCAAC;;;;;;;;;;;;;;;;;;;0BAKvE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,SAAQ;wBAAkB,WAAU;kCAAyC;;;;;;kCAGpF,6LAAC;wBACC,IAAG;wBACH,OAAO,QAAQ,QAAQ,IAAI;wBAC3B,UAAU,CAAC,IAAM,mBAAmB,YAAY,EAAE,MAAM,CAAC,KAAK;wBAC9D,WAAU;;0CAEV,6LAAC;gCAAO,OAAM;0CAAG;;;;;;0CACjB,6LAAC;gCAAO,OAAM;;oCAAO;oCAAO,UAAU,UAAU,CAAC,IAAI;oCAAC;;;;;;;0CACtD,6LAAC;gCAAO,OAAM;;oCAAS;oCAAS,UAAU,UAAU,CAAC,MAAM;oCAAC;;;;;;;0CAC5D,6LAAC;gCAAO,OAAM;;oCAAM;oCAAM,UAAU,UAAU,CAAC,GAAG;oCAAC;;;;;;;;;;;;;;;;;;;0BAKvD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,SAAQ;wBAAc,WAAU;kCAAyC;;;;;;kCAGhF,6LAAC;wBACC,IAAG;wBACH,OAAO,QAAQ,MAAM,IAAI;wBACzB,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK;wBAC5D,WAAU;;0CAEV,6LAAC;gCAAO,OAAM;0CAAY;;;;;;0CAC1B,6LAAC;gCAAO,OAAM;0CAAY;;;;;;0CAC1B,6LAAC;gCAAO,OAAM;0CAAQ;;;;;;0CACtB,6LAAC;gCAAO,OAAM;0CAAW;;;;;;0CACzB,6LAAC;gCAAO,OAAM;0CAAU;;;;;;;;;;;;;;;;;;0BAK5B,6LAAC;gBACC,SAAS,IAAM,mBAAmB,aAAa,QAAQ,SAAS,KAAK,SAAS,QAAQ;gBACtF,WAAU;gBACV,OAAO,CAAC,KAAK,EAAE,QAAQ,SAAS,KAAK,SAAS,cAAc,cAAc;;oBAEzE,QAAQ,SAAS,KAAK,uBACrB,6LAAC,oOAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;6CAEpB,6LAAC,iOAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCAErB,6LAAC;kCAAM,QAAQ,SAAS,KAAK,SAAS,SAAS;;;;;;;;;;;;YAIhD,kCACC,6LAAC;gBACC,SAAS;gBACT,WAAU;;kCAEV,6LAAC,+LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;kCACb,6LAAC;kCAAK;;;;;;;;;;;;YAKT,kCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,6LAAC;;4BAAM,OAAO,IAAI,CAAC,SAAS,MAAM;4BAAC;4BAAQ,OAAO,IAAI,CAAC,SAAS,MAAM,KAAK,IAAI,MAAM;4BAAG;;;;;;;;;;;;;;;;;;;AAKlG;KA9GwB", "debugId": null}}, {"offset": {"line": 447, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Choreo/frontend/src/components/TaskForm.tsx"], "sourcesContent": ["'use client'\n\nimport { CreateTaskRequest, Task, UpdateTaskRequest } from '@/types'\nimport { Calendar, Clock, Flag, X } from 'lucide-react'\nimport { useEffect, useState } from 'react'\nimport { useForm } from 'react-hook-form'\n\ninterface TaskFormProps {\n  task?: Task | null\n  onSubmit: (data: CreateTaskRequest | UpdateTaskRequest) => void\n  onCancel: () => void\n  isLoading?: boolean\n}\n\ninterface FormData {\n  title: string\n  description: string\n  priority: 'low' | 'medium' | 'high'\n  status: 'todo' | 'in-progress' | 'completed'\n  dueDate: string\n}\n\nexport default function TaskForm({ task, onSubmit, onCancel, isLoading = false }: TaskFormProps) {\n  const [isSubmitting, setIsSubmitting] = useState(false)\n  const isEditing = !!task\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n    reset,\n    watch\n  } = useForm<FormData>({\n    defaultValues: {\n      title: task?.title || '',\n      description: task?.description || '',\n      priority: task?.priority || 'medium',\n      status: task?.status || 'todo',\n      dueDate: task?.dueDate ? new Date(task.dueDate).toISOString().split('T')[0] : ''\n    }\n  })\n\n  // Reset form when task changes\n  useEffect(() => {\n    if (task) {\n      reset({\n        title: task.title,\n        description: task.description,\n        priority: task.priority,\n        status: task.status,\n        dueDate: task.dueDate ? new Date(task.dueDate).toISOString().split('T')[0] : ''\n      })\n    }\n  }, [task, reset])\n\n  const onFormSubmit = async (data: FormData) => {\n    setIsSubmitting(true)\n    \n    try {\n      const submitData = {\n        title: data.title.trim(),\n        description: data.description.trim(),\n        priority: data.priority,\n        status: data.status,\n        dueDate: data.dueDate ? new Date(data.dueDate).toISOString() : null\n      }\n\n      await onSubmit(submitData)\n    } catch (error) {\n      console.error('Form submission error:', error)\n    } finally {\n      setIsSubmitting(false)\n    }\n  }\n\n  const handleCancel = () => {\n    reset()\n    onCancel()\n  }\n\n  // Close modal on escape key\n  useEffect(() => {\n    const handleEscape = (e: KeyboardEvent) => {\n      if (e.key === 'Escape') {\n        handleCancel()\n      }\n    }\n\n    document.addEventListener('keydown', handleEscape)\n    return () => document.removeEventListener('keydown', handleEscape)\n  }, []) // eslint-disable-line react-hooks/exhaustive-deps\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n      <div className=\"bg-white rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-secondary-200\">\n          <h2 className=\"text-xl font-semibold text-secondary-900\">\n            {isEditing ? 'Edit Task' : 'Create New Task'}\n          </h2>\n          <button\n            onClick={handleCancel}\n            className=\"p-2 text-secondary-400 hover:text-secondary-600 hover:bg-secondary-100 rounded-lg transition-colors duration-200\"\n          >\n            <X className=\"w-5 h-5\" />\n          </button>\n        </div>\n\n        {/* Form */}\n        <form onSubmit={handleSubmit(onFormSubmit)} className=\"p-6 space-y-6\">\n          {/* Title */}\n          <div>\n            <label htmlFor=\"title\" className=\"form-label\">\n              Task Title *\n            </label>\n            <input\n              id=\"title\"\n              type=\"text\"\n              {...register('title', {\n                required: 'Task title is required',\n                minLength: {\n                  value: 1,\n                  message: 'Title must be at least 1 character'\n                },\n                maxLength: {\n                  value: 200,\n                  message: 'Title must be less than 200 characters'\n                }\n              })}\n              className={`form-input ${errors.title ? 'border-danger-300 focus:border-danger-500 focus:ring-danger-500' : ''}`}\n              placeholder=\"Enter task title...\"\n              disabled={isSubmitting || isLoading}\n            />\n            {errors.title && (\n              <p className=\"form-error\">{errors.title.message}</p>\n            )}\n          </div>\n\n          {/* Description */}\n          <div>\n            <label htmlFor=\"description\" className=\"form-label\">\n              Description\n            </label>\n            <textarea\n              id=\"description\"\n              rows={3}\n              {...register('description', {\n                maxLength: {\n                  value: 1000,\n                  message: 'Description must be less than 1000 characters'\n                }\n              })}\n              className={`form-input ${errors.description ? 'border-danger-300 focus:border-danger-500 focus:ring-danger-500' : ''}`}\n              placeholder=\"Enter task description...\"\n              disabled={isSubmitting || isLoading}\n            />\n            {errors.description && (\n              <p className=\"form-error\">{errors.description.message}</p>\n            )}\n          </div>\n\n          {/* Priority and Status Row */}\n          <div className=\"grid grid-cols-2 gap-4\">\n            {/* Priority */}\n            <div>\n              <label htmlFor=\"priority\" className=\"form-label flex items-center space-x-1\">\n                <Flag className=\"w-4 h-4\" />\n                <span>Priority</span>\n              </label>\n              <select\n                id=\"priority\"\n                {...register('priority')}\n                className=\"form-input\"\n                disabled={isSubmitting || isLoading}\n              >\n                <option value=\"low\">Low</option>\n                <option value=\"medium\">Medium</option>\n                <option value=\"high\">High</option>\n              </select>\n            </div>\n\n            {/* Status */}\n            <div>\n              <label htmlFor=\"status\" className=\"form-label flex items-center space-x-1\">\n                <Clock className=\"w-4 h-4\" />\n                <span>Status</span>\n              </label>\n              <select\n                id=\"status\"\n                {...register('status')}\n                className=\"form-input\"\n                disabled={isSubmitting || isLoading}\n              >\n                <option value=\"todo\">To Do</option>\n                <option value=\"in-progress\">In Progress</option>\n                <option value=\"completed\">Completed</option>\n              </select>\n            </div>\n          </div>\n\n          {/* Due Date */}\n          <div>\n            <label htmlFor=\"dueDate\" className=\"form-label flex items-center space-x-1\">\n              <Calendar className=\"w-4 h-4\" />\n              <span>Due Date</span>\n            </label>\n            <input\n              id=\"dueDate\"\n              type=\"date\"\n              {...register('dueDate')}\n              className=\"form-input\"\n              disabled={isSubmitting || isLoading}\n              min={new Date().toISOString().split('T')[0]}\n            />\n          </div>\n\n          {/* Form Actions */}\n          <div className=\"flex items-center justify-end space-x-3 pt-4 border-t border-secondary-200\">\n            <button\n              type=\"button\"\n              onClick={handleCancel}\n              className=\"btn-outline\"\n              disabled={isSubmitting || isLoading}\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              className=\"btn-primary flex items-center space-x-2\"\n              disabled={isSubmitting || isLoading}\n            >\n              {(isSubmitting || isLoading) && (\n                <div className=\"spinner w-4 h-4\"></div>\n              )}\n              <span>\n                {isSubmitting || isLoading \n                  ? (isEditing ? 'Updating...' : 'Creating...') \n                  : (isEditing ? 'Update Task' : 'Create Task')\n                }\n              </span>\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AACA;AACA;;;AALA;;;;AAsBe,SAAS,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,KAAK,EAAiB;;IAC7F,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,YAAY,CAAC,CAAC;IAEpB,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,KAAK,EACL,KAAK,EACN,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAY;QACpB,eAAe;YACb,OAAO,MAAM,SAAS;YACtB,aAAa,MAAM,eAAe;YAClC,UAAU,MAAM,YAAY;YAC5B,QAAQ,MAAM,UAAU;YACxB,SAAS,MAAM,UAAU,IAAI,KAAK,KAAK,OAAO,EAAE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;QAChF;IACF;IAEA,+BAA+B;IAC/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,MAAM;gBACR,MAAM;oBACJ,OAAO,KAAK,KAAK;oBACjB,aAAa,KAAK,WAAW;oBAC7B,UAAU,KAAK,QAAQ;oBACvB,QAAQ,KAAK,MAAM;oBACnB,SAAS,KAAK,OAAO,GAAG,IAAI,KAAK,KAAK,OAAO,EAAE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;gBAC/E;YACF;QACF;6BAAG;QAAC;QAAM;KAAM;IAEhB,MAAM,eAAe,OAAO;QAC1B,gBAAgB;QAEhB,IAAI;YACF,MAAM,aAAa;gBACjB,OAAO,KAAK,KAAK,CAAC,IAAI;gBACtB,aAAa,KAAK,WAAW,CAAC,IAAI;gBAClC,UAAU,KAAK,QAAQ;gBACvB,QAAQ,KAAK,MAAM;gBACnB,SAAS,KAAK,OAAO,GAAG,IAAI,KAAK,KAAK,OAAO,EAAE,WAAW,KAAK;YACjE;YAEA,MAAM,SAAS;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,eAAe;QACnB;QACA;IACF;IAEA,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM;mDAAe,CAAC;oBACpB,IAAI,EAAE,GAAG,KAAK,UAAU;wBACtB;oBACF;gBACF;;YAEA,SAAS,gBAAgB,CAAC,WAAW;YACrC;sCAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;;QACvD;6BAAG,EAAE,EAAE,kDAAkD;;IAEzD,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,YAAY,cAAc;;;;;;sCAE7B,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAKjB,6LAAC;oBAAK,UAAU,aAAa;oBAAe,WAAU;;sCAEpD,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAQ,WAAU;8CAAa;;;;;;8CAG9C,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACJ,GAAG,SAAS,SAAS;wCACpB,UAAU;wCACV,WAAW;4CACT,OAAO;4CACP,SAAS;wCACX;wCACA,WAAW;4CACT,OAAO;4CACP,SAAS;wCACX;oCACF,EAAE;oCACF,WAAW,CAAC,WAAW,EAAE,OAAO,KAAK,GAAG,oEAAoE,IAAI;oCAChH,aAAY;oCACZ,UAAU,gBAAgB;;;;;;gCAE3B,OAAO,KAAK,kBACX,6LAAC;oCAAE,WAAU;8CAAc,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;sCAKnD,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAc,WAAU;8CAAa;;;;;;8CAGpD,6LAAC;oCACC,IAAG;oCACH,MAAM;oCACL,GAAG,SAAS,eAAe;wCAC1B,WAAW;4CACT,OAAO;4CACP,SAAS;wCACX;oCACF,EAAE;oCACF,WAAW,CAAC,WAAW,EAAE,OAAO,WAAW,GAAG,oEAAoE,IAAI;oCACtH,aAAY;oCACZ,UAAU,gBAAgB;;;;;;gCAE3B,OAAO,WAAW,kBACjB,6LAAC;oCAAE,WAAU;8CAAc,OAAO,WAAW,CAAC,OAAO;;;;;;;;;;;;sCAKzD,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAW,WAAU;;8DAClC,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC;4CACC,IAAG;4CACF,GAAG,SAAS,WAAW;4CACxB,WAAU;4CACV,UAAU,gBAAgB;;8DAE1B,6LAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,6LAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;;;;;;;;;;;;;8CAKzB,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAS,WAAU;;8DAChC,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC;4CACC,IAAG;4CACF,GAAG,SAAS,SAAS;4CACtB,WAAU;4CACV,UAAU,gBAAgB;;8DAE1B,6LAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,6LAAC;oDAAO,OAAM;8DAAc;;;;;;8DAC5B,6LAAC;oDAAO,OAAM;8DAAY;;;;;;;;;;;;;;;;;;;;;;;;sCAMhC,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAU,WAAU;;sDACjC,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACJ,GAAG,SAAS,UAAU;oCACvB,WAAU;oCACV,UAAU,gBAAgB;oCAC1B,KAAK,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;;;;;;;sCAK/C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,UAAU,gBAAgB;8CAC3B;;;;;;8CAGD,6LAAC;oCACC,MAAK;oCACL,WAAU;oCACV,UAAU,gBAAgB;;wCAEzB,CAAC,gBAAgB,SAAS,mBACzB,6LAAC;4CAAI,WAAU;;;;;;sDAEjB,6LAAC;sDACE,gBAAgB,YACZ,YAAY,gBAAgB,gBAC5B,YAAY,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASjD;GAhOwB;;QAUlB,iKAAA,CAAA,UAAO;;;KAVW", "debugId": null}}, {"offset": {"line": 930, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Choreo/frontend/src/components/ConfirmDialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { AlertTriangle, X } from 'lucide-react'\n\ninterface ConfirmDialogProps {\n  isOpen: boolean\n  title: string\n  message: string\n  confirmText?: string\n  cancelText?: string\n  variant?: 'danger' | 'warning' | 'info'\n  onConfirm: () => void\n  onCancel: () => void\n  isLoading?: boolean\n}\n\nexport default function ConfirmDialog({\n  isOpen,\n  title,\n  message,\n  confirmText = 'Confirm',\n  cancelText = 'Cancel',\n  variant = 'danger',\n  onConfirm,\n  onCancel,\n  isLoading = false\n}: ConfirmDialogProps) {\n  // Close dialog on escape key\n  useEffect(() => {\n    const handleEscape = (e: KeyboardEvent) => {\n      if (e.key === 'Escape' && !isLoading) {\n        onCancel()\n      }\n    }\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape)\n      // Prevent body scroll when dialog is open\n      document.body.style.overflow = 'hidden'\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape)\n      document.body.style.overflow = 'unset'\n    }\n  }, [isOpen, isLoading, onCancel])\n\n  if (!isOpen) return null\n\n  const variantStyles = {\n    danger: {\n      icon: 'text-danger-600',\n      button: 'btn-danger'\n    },\n    warning: {\n      icon: 'text-warning-600',\n      button: 'btn-warning'\n    },\n    info: {\n      icon: 'text-primary-600',\n      button: 'btn-primary'\n    }\n  }\n\n  const styles = variantStyles[variant]\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n      <div className=\"bg-white rounded-xl shadow-xl max-w-md w-full\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-secondary-200\">\n          <div className=\"flex items-center space-x-3\">\n            <div className={`w-8 h-8 rounded-full bg-opacity-20 flex items-center justify-center ${\n              variant === 'danger' ? 'bg-danger-100' : \n              variant === 'warning' ? 'bg-warning-100' : 'bg-primary-100'\n            }`}>\n              <AlertTriangle className={`w-5 h-5 ${styles.icon}`} />\n            </div>\n            <h2 className=\"text-lg font-semibold text-secondary-900\">\n              {title}\n            </h2>\n          </div>\n          \n          {!isLoading && (\n            <button\n              onClick={onCancel}\n              className=\"p-2 text-secondary-400 hover:text-secondary-600 hover:bg-secondary-100 rounded-lg transition-colors duration-200\"\n            >\n              <X className=\"w-5 h-5\" />\n            </button>\n          )}\n        </div>\n\n        {/* Content */}\n        <div className=\"p-6\">\n          <p className=\"text-secondary-600 leading-relaxed\">\n            {message}\n          </p>\n        </div>\n\n        {/* Actions */}\n        <div className=\"flex items-center justify-end space-x-3 p-6 border-t border-secondary-200 bg-secondary-50 rounded-b-xl\">\n          <button\n            onClick={onCancel}\n            disabled={isLoading}\n            className=\"btn-outline\"\n          >\n            {cancelText}\n          </button>\n          \n          <button\n            onClick={onConfirm}\n            disabled={isLoading}\n            className={`${styles.button} flex items-center space-x-2`}\n          >\n            {isLoading && (\n              <div className=\"spinner w-4 h-4\"></div>\n            )}\n            <span>\n              {isLoading ? 'Processing...' : confirmText}\n            </span>\n          </button>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AAiBe,SAAS,cAAc,EACpC,MAAM,EACN,KAAK,EACL,OAAO,EACP,cAAc,SAAS,EACvB,aAAa,QAAQ,EACrB,UAAU,QAAQ,EAClB,SAAS,EACT,QAAQ,EACR,YAAY,KAAK,EACE;;IACnB,6BAA6B;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;wDAAe,CAAC;oBACpB,IAAI,EAAE,GAAG,KAAK,YAAY,CAAC,WAAW;wBACpC;oBACF;gBACF;;YAEA,IAAI,QAAQ;gBACV,SAAS,gBAAgB,CAAC,WAAW;gBACrC,0CAA0C;gBAC1C,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;2CAAO;oBACL,SAAS,mBAAmB,CAAC,WAAW;oBACxC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;kCAAG;QAAC;QAAQ;QAAW;KAAS;IAEhC,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,gBAAgB;QACpB,QAAQ;YACN,MAAM;YACN,QAAQ;QACV;QACA,SAAS;YACP,MAAM;YACN,QAAQ;QACV;QACA,MAAM;YACJ,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,SAAS,aAAa,CAAC,QAAQ;IAErC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAW,CAAC,oEAAoE,EACnF,YAAY,WAAW,kBACvB,YAAY,YAAY,mBAAmB,kBAC3C;8CACA,cAAA,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAW,CAAC,QAAQ,EAAE,OAAO,IAAI,EAAE;;;;;;;;;;;8CAEpD,6LAAC;oCAAG,WAAU;8CACX;;;;;;;;;;;;wBAIJ,CAAC,2BACA,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAMnB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;8BAKL,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAU;sCAET;;;;;;sCAGH,6LAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAW,GAAG,OAAO,MAAM,CAAC,4BAA4B,CAAC;;gCAExD,2BACC,6LAAC;oCAAI,WAAU;;;;;;8CAEjB,6LAAC;8CACE,YAAY,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7C;GA9GwB;KAAA", "debugId": null}}, {"offset": {"line": 1131, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Choreo/frontend/src/components/TaskList.tsx"], "sourcesContent": ["'use client'\n\nimport { Task } from '@/types'\nimport {\n    AlertCircle,\n    Calendar,\n    CheckCircle,\n    Circle,\n    Clock,\n    Edit2,\n    Flag,\n    MoreVertical,\n    Trash2\n} from 'lucide-react'\nimport { useState } from 'react'\nimport ConfirmDialog from './ConfirmDialog'\n\ninterface TaskListProps {\n  tasks: Task[]\n  onEdit: (task: Task) => void\n  onDelete: (taskId: string) => void\n  onUpdate: () => void\n}\n\nexport default function TaskList({ tasks, onEdit, onDelete, onUpdate }: TaskListProps) {\n  const [deleteTaskId, setDeleteTaskId] = useState<string | null>(null)\n  const [showDropdown, setShowDropdown] = useState<string | null>(null)\n\n  const handleDeleteClick = (taskId: string) => {\n    setDeleteTaskId(taskId)\n    setShowDropdown(null)\n  }\n\n  const handleDeleteConfirm = () => {\n    if (deleteTaskId) {\n      onDelete(deleteTaskId)\n      setDeleteTaskId(null)\n    }\n  }\n\n  const handleDeleteCancel = () => {\n    setDeleteTaskId(null)\n  }\n\n  const handleEditClick = (task: Task) => {\n    onEdit(task)\n    setShowDropdown(null)\n  }\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority) {\n      case 'high':\n        return 'text-danger-600 bg-danger-50'\n      case 'medium':\n        return 'text-warning-600 bg-warning-50'\n      case 'low':\n        return 'text-success-600 bg-success-50'\n      default:\n        return 'text-secondary-600 bg-secondary-50'\n    }\n  }\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'completed':\n        return <CheckCircle className=\"w-5 h-5 text-success-600\" />\n      case 'in-progress':\n        return <Clock className=\"w-5 h-5 text-warning-600\" />\n      case 'todo':\n        return <Circle className=\"w-5 h-5 text-secondary-400\" />\n      default:\n        return <Circle className=\"w-5 h-5 text-secondary-400\" />\n    }\n  }\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'completed':\n        return 'text-success-700 bg-success-50 border-success-200'\n      case 'in-progress':\n        return 'text-warning-700 bg-warning-50 border-warning-200'\n      case 'todo':\n        return 'text-secondary-700 bg-secondary-50 border-secondary-200'\n      default:\n        return 'text-secondary-700 bg-secondary-50 border-secondary-200'\n    }\n  }\n\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString)\n    return date.toLocaleDateString('en-US', {\n      month: 'short',\n      day: 'numeric',\n      year: 'numeric'\n    })\n  }\n\n  const isOverdue = (dueDate: string | null) => {\n    if (!dueDate) return false\n    return new Date(dueDate) < new Date()\n  }\n\n  if (tasks.length === 0) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"w-16 h-16 mx-auto mb-4 bg-secondary-100 rounded-full flex items-center justify-center\">\n          <CheckCircle className=\"w-8 h-8 text-secondary-400\" />\n        </div>\n        <h3 className=\"text-lg font-medium text-secondary-900 mb-2\">\n          No tasks found\n        </h3>\n        <p className=\"text-secondary-600\">\n          Create your first task to get started with managing your work.\n        </p>\n      </div>\n    )\n  }\n\n  return (\n    <>\n      <div className=\"divide-y divide-secondary-200\">\n        {tasks.map((task) => (\n          <div\n            key={task.id}\n            className=\"p-4 hover:bg-secondary-50 transition-colors duration-200\"\n          >\n            <div className=\"flex items-start justify-between\">\n              <div className=\"flex items-start space-x-3 flex-1\">\n                {/* Status Icon */}\n                <div className=\"flex-shrink-0 mt-1\">\n                  {getStatusIcon(task.status)}\n                </div>\n\n                {/* Task Content */}\n                <div className=\"flex-1 min-w-0\">\n                  <div className=\"flex items-start justify-between\">\n                    <div className=\"flex-1\">\n                      <h3 className={`text-sm font-medium ${\n                        task.status === 'completed' \n                          ? 'text-secondary-500 line-through' \n                          : 'text-secondary-900'\n                      }`}>\n                        {task.title}\n                      </h3>\n                      \n                      {task.description && (\n                        <p className={`mt-1 text-sm ${\n                          task.status === 'completed'\n                            ? 'text-secondary-400'\n                            : 'text-secondary-600'\n                        }`}>\n                          {task.description}\n                        </p>\n                      )}\n                    </div>\n                  </div>\n\n                  {/* Task Meta */}\n                  <div className=\"mt-3 flex items-center space-x-4 text-xs\">\n                    {/* Priority */}\n                    <span className={`inline-flex items-center px-2 py-1 rounded-full font-medium ${getPriorityColor(task.priority)}`}>\n                      <Flag className=\"w-3 h-3 mr-1\" />\n                      {task.priority}\n                    </span>\n\n                    {/* Status */}\n                    <span className={`inline-flex items-center px-2 py-1 rounded-full border font-medium ${getStatusColor(task.status)}`}>\n                      {task.status.replace('-', ' ')}\n                    </span>\n\n                    {/* Due Date */}\n                    {task.dueDate && (\n                      <span className={`inline-flex items-center ${\n                        isOverdue(task.dueDate) \n                          ? 'text-danger-600' \n                          : 'text-secondary-500'\n                      }`}>\n                        <Calendar className=\"w-3 h-3 mr-1\" />\n                        {formatDate(task.dueDate)}\n                        {isOverdue(task.dueDate) && (\n                          <AlertCircle className=\"w-3 h-3 ml-1\" />\n                        )}\n                      </span>\n                    )}\n\n                    {/* Created Date */}\n                    <span className=\"text-secondary-400\">\n                      Created {formatDate(task.createdAt)}\n                    </span>\n                  </div>\n                </div>\n              </div>\n\n              {/* Actions */}\n              <div className=\"relative flex-shrink-0 ml-4\">\n                <button\n                  onClick={() => setShowDropdown(showDropdown === task.id ? null : task.id)}\n                  className=\"p-1 rounded-md text-secondary-400 hover:text-secondary-600 hover:bg-secondary-100 transition-colors duration-200\"\n                >\n                  <MoreVertical className=\"w-4 h-4\" />\n                </button>\n\n                {/* Dropdown Menu */}\n                {showDropdown === task.id && (\n                  <div className=\"absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg border border-secondary-200 z-10\">\n                    <div className=\"py-1\">\n                      <button\n                        onClick={() => handleEditClick(task)}\n                        className=\"flex items-center w-full px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50 transition-colors duration-200\"\n                      >\n                        <Edit2 className=\"w-4 h-4 mr-3\" />\n                        Edit Task\n                      </button>\n                      <button\n                        onClick={() => handleDeleteClick(task.id)}\n                        className=\"flex items-center w-full px-4 py-2 text-sm text-danger-700 hover:bg-danger-50 transition-colors duration-200\"\n                      >\n                        <Trash2 className=\"w-4 h-4 mr-3\" />\n                        Delete Task\n                      </button>\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Delete Confirmation Dialog */}\n      <ConfirmDialog\n        isOpen={!!deleteTaskId}\n        title=\"Delete Task\"\n        message=\"Are you sure you want to delete this task? This action cannot be undone.\"\n        confirmText=\"Delete\"\n        cancelText=\"Cancel\"\n        onConfirm={handleDeleteConfirm}\n        onCancel={handleDeleteCancel}\n        variant=\"danger\"\n      />\n\n      {/* Click outside to close dropdown */}\n      {showDropdown && (\n        <div\n          className=\"fixed inset-0 z-0\"\n          onClick={() => setShowDropdown(null)}\n        />\n      )}\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;;;AAfA;;;;AAwBe,SAAS,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAiB;;IACnF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhE,MAAM,oBAAoB,CAAC;QACzB,gBAAgB;QAChB,gBAAgB;IAClB;IAEA,MAAM,sBAAsB;QAC1B,IAAI,cAAc;YAChB,SAAS;YACT,gBAAgB;QAClB;IACF;IAEA,MAAM,qBAAqB;QACzB,gBAAgB;IAClB;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAO;QACP,gBAAgB;IAClB;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B;gBACE,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;QAC7B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,OAAO;YACP,KAAK;YACL,MAAM;QACR;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,IAAI,CAAC,SAAS,OAAO;QACrB,OAAO,IAAI,KAAK,WAAW,IAAI;IACjC;IAEA,IAAI,MAAM,MAAM,KAAK,GAAG;QACtB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;;;;;;8BAEzB,6LAAC;oBAAG,WAAU;8BAA8C;;;;;;8BAG5D,6LAAC;oBAAE,WAAU;8BAAqB;;;;;;;;;;;;IAKxC;IAEA,qBACE;;0BACE,6LAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;wBAEC,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;sDACZ,cAAc,KAAK,MAAM;;;;;;sDAI5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAW,CAAC,oBAAoB,EAClC,KAAK,MAAM,KAAK,cACZ,oCACA,sBACJ;0EACC,KAAK,KAAK;;;;;;4DAGZ,KAAK,WAAW,kBACf,6LAAC;gEAAE,WAAW,CAAC,aAAa,EAC1B,KAAK,MAAM,KAAK,cACZ,uBACA,sBACJ;0EACC,KAAK,WAAW;;;;;;;;;;;;;;;;;8DAOzB,6LAAC;oDAAI,WAAU;;sEAEb,6LAAC;4DAAK,WAAW,CAAC,4DAA4D,EAAE,iBAAiB,KAAK,QAAQ,GAAG;;8EAC/G,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEACf,KAAK,QAAQ;;;;;;;sEAIhB,6LAAC;4DAAK,WAAW,CAAC,mEAAmE,EAAE,eAAe,KAAK,MAAM,GAAG;sEACjH,KAAK,MAAM,CAAC,OAAO,CAAC,KAAK;;;;;;wDAI3B,KAAK,OAAO,kBACX,6LAAC;4DAAK,WAAW,CAAC,yBAAyB,EACzC,UAAU,KAAK,OAAO,IAClB,oBACA,sBACJ;;8EACA,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEACnB,WAAW,KAAK,OAAO;gEACvB,UAAU,KAAK,OAAO,mBACrB,6LAAC,uNAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;;;;;;;sEAM7B,6LAAC;4DAAK,WAAU;;gEAAqB;gEAC1B,WAAW,KAAK,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;8CAO1C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,gBAAgB,iBAAiB,KAAK,EAAE,GAAG,OAAO,KAAK,EAAE;4CACxE,WAAU;sDAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;wCAIzB,iBAAiB,KAAK,EAAE,kBACvB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,SAAS,IAAM,gBAAgB;wDAC/B,WAAU;;0EAEV,6LAAC,qMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGpC,6LAAC;wDACC,SAAS,IAAM,kBAAkB,KAAK,EAAE;wDACxC,WAAU;;0EAEV,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBA9F1C,KAAK,EAAE;;;;;;;;;;0BA2GlB,6LAAC,sIAAA,CAAA,UAAa;gBACZ,QAAQ,CAAC,CAAC;gBACV,OAAM;gBACN,SAAQ;gBACR,aAAY;gBACZ,YAAW;gBACX,WAAW;gBACX,UAAU;gBACV,SAAQ;;;;;;YAIT,8BACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,gBAAgB;;;;;;;;AAKzC;GAlOwB;KAAA", "debugId": null}}, {"offset": {"line": 1552, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Choreo/frontend/src/components/Toast.tsx"], "sourcesContent": ["'use client'\n\nimport { AlertCircle, CheckCircle, Info, X } from 'lucide-react'\nimport { useEffect, useState } from 'react'\n\nexport interface ToastProps {\n  id: string\n  type: 'success' | 'error' | 'info' | 'warning'\n  title: string\n  message?: string\n  duration?: number\n  onClose: (id: string) => void\n}\n\nexport default function Toast({\n  id,\n  type,\n  title,\n  message,\n  duration = 5000,\n  onClose\n}: ToastProps) {\n  const [isVisible, setIsVisible] = useState(false)\n  const [isExiting, setIsExiting] = useState(false)\n\n  useEffect(() => {\n    // Trigger entrance animation\n    const timer = setTimeout(() => setIsVisible(true), 10)\n    return () => clearTimeout(timer)\n  }, [])\n\n  useEffect(() => {\n    if (duration > 0) {\n      const timer = setTimeout(() => {\n        handleClose()\n      }, duration)\n      return () => clearTimeout(timer)\n    }\n  }, [duration]) // eslint-disable-line react-hooks/exhaustive-deps\n\n  const handleClose = () => {\n    setIsExiting(true)\n    setTimeout(() => {\n      onClose(id)\n    }, 300) // Match the exit animation duration\n  }\n\n  const getIcon = () => {\n    switch (type) {\n      case 'success':\n        return <CheckCircle className=\"w-5 h-5 text-success-600\" />\n      case 'error':\n        return <AlertCircle className=\"w-5 h-5 text-danger-600\" />\n      case 'warning':\n        return <AlertCircle className=\"w-5 h-5 text-warning-600\" />\n      case 'info':\n        return <Info className=\"w-5 h-5 text-primary-600\" />\n      default:\n        return <Info className=\"w-5 h-5 text-primary-600\" />\n    }\n  }\n\n  const getStyles = () => {\n    switch (type) {\n      case 'success':\n        return 'bg-success-50 border-success-200'\n      case 'error':\n        return 'bg-danger-50 border-danger-200'\n      case 'warning':\n        return 'bg-warning-50 border-warning-200'\n      case 'info':\n        return 'bg-primary-50 border-primary-200'\n      default:\n        return 'bg-primary-50 border-primary-200'\n    }\n  }\n\n  return (\n    <div\n      className={`\n        transform transition-all duration-300 ease-in-out\n        ${isVisible && !isExiting ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'}\n        ${getStyles()}\n        border rounded-lg shadow-lg p-4 mb-3 max-w-sm w-full\n      `}\n    >\n      <div className=\"flex items-start\">\n        <div className=\"flex-shrink-0\">\n          {getIcon()}\n        </div>\n        \n        <div className=\"ml-3 flex-1\">\n          <h4 className=\"text-sm font-medium text-secondary-900\">\n            {title}\n          </h4>\n          {message && (\n            <p className=\"mt-1 text-sm text-secondary-600\">\n              {message}\n            </p>\n          )}\n        </div>\n        \n        <button\n          onClick={handleClose}\n          className=\"ml-4 flex-shrink-0 p-1 rounded-md text-secondary-400 hover:text-secondary-600 transition-colors duration-200\"\n        >\n          <X className=\"w-4 h-4\" />\n        </button>\n      </div>\n    </div>\n  )\n}\n\n// Toast Container Component\ninterface ToastContainerProps {\n  toasts: ToastProps[]\n  onClose: (id: string) => void\n}\n\nexport function ToastContainer({ toasts, onClose }: ToastContainerProps) {\n  return (\n    <div className=\"fixed top-4 right-4 z-50 space-y-2\">\n      {toasts.map((toast) => (\n        <Toast\n          key={toast.id}\n          {...toast}\n          onClose={onClose}\n        />\n      ))}\n    </div>\n  )\n}\n\n// Toast Hook for managing toasts\nexport function useToast() {\n  const [toasts, setToasts] = useState<ToastProps[]>([])\n\n  const addToast = (toast: Omit<ToastProps, 'id' | 'onClose'>) => {\n    const id = Math.random().toString(36).substr(2, 9)\n    const newToast: ToastProps = {\n      ...toast,\n      id,\n      onClose: removeToast\n    }\n    setToasts(prev => [...prev, newToast])\n  }\n\n  const removeToast = (id: string) => {\n    setToasts(prev => prev.filter(toast => toast.id !== id))\n  }\n\n  const success = (title: string, message?: string) => {\n    addToast({ type: 'success', title, message })\n  }\n\n  const error = (title: string, message?: string) => {\n    addToast({ type: 'error', title, message })\n  }\n\n  const warning = (title: string, message?: string) => {\n    addToast({ type: 'warning', title, message })\n  }\n\n  const info = (title: string, message?: string) => {\n    addToast({ type: 'info', title, message })\n  }\n\n  return {\n    toasts,\n    addToast,\n    removeToast,\n    success,\n    error,\n    warning,\n    info\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AAAA;AAAA;AAAA;AACA;;;AAHA;;;AAce,SAAS,MAAM,EAC5B,EAAE,EACF,IAAI,EACJ,KAAK,EACL,OAAO,EACP,WAAW,IAAI,EACf,OAAO,EACI;;IACX,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,6BAA6B;YAC7B,MAAM,QAAQ;yCAAW,IAAM,aAAa;wCAAO;YACnD;mCAAO,IAAM,aAAa;;QAC5B;0BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,IAAI,WAAW,GAAG;gBAChB,MAAM,QAAQ;6CAAW;wBACvB;oBACF;4CAAG;gBACH;uCAAO,IAAM,aAAa;;YAC5B;QACF;0BAAG;QAAC;KAAS,EAAE,kDAAkD;;IAEjE,MAAM,cAAc;QAClB,aAAa;QACb,WAAW;YACT,QAAQ;QACV,GAAG,KAAK,oCAAoC;;IAC9C;IAEA,MAAM,UAAU;QACd,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB;gBACE,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,YAAY;QAChB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QACC,WAAW,CAAC;;QAEV,EAAE,aAAa,CAAC,YAAY,8BAA8B,6BAA6B;QACvF,EAAE,YAAY;;MAEhB,CAAC;kBAED,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACZ;;;;;;8BAGH,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX;;;;;;wBAEF,yBACC,6LAAC;4BAAE,WAAU;sCACV;;;;;;;;;;;;8BAKP,6LAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKvB;GAjGwB;KAAA;AAyGjB,SAAS,eAAe,EAAE,MAAM,EAAE,OAAO,EAAuB;IACrE,qBACE,6LAAC;QAAI,WAAU;kBACZ,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC;gBAEE,GAAG,KAAK;gBACT,SAAS;eAFJ,MAAM,EAAE;;;;;;;;;;AAOvB;MAZgB;AAeT,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAErD,MAAM,WAAW,CAAC;QAChB,MAAM,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;QAChD,MAAM,WAAuB;YAC3B,GAAG,KAAK;YACR;YACA,SAAS;QACX;QACA,UAAU,CAAA,OAAQ;mBAAI;gBAAM;aAAS;IACvC;IAEA,MAAM,cAAc,CAAC;QACnB,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACtD;IAEA,MAAM,UAAU,CAAC,OAAe;QAC9B,SAAS;YAAE,MAAM;YAAW;YAAO;QAAQ;IAC7C;IAEA,MAAM,QAAQ,CAAC,OAAe;QAC5B,SAAS;YAAE,MAAM;YAAS;YAAO;QAAQ;IAC3C;IAEA,MAAM,UAAU,CAAC,OAAe;QAC9B,SAAS;YAAE,MAAM;YAAW;YAAO;QAAQ;IAC7C;IAEA,MAAM,OAAO,CAAC,OAAe;QAC3B,SAAS;YAAE,MAAM;YAAQ;YAAO;QAAQ;IAC1C;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;IA1CgB", "debugId": null}}, {"offset": {"line": 1824, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Choreo/frontend/src/components/UserProfile.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport { User, LogOut, Settings, Activity, ChevronDown } from 'lucide-react'\nimport { User as UserType } from '@/types'\n\ninterface UserProfileProps {\n  user: UserType\n  onLogout: () => void\n}\n\nexport default function UserProfile({ user, onLogout }: UserProfileProps) {\n  const [isOpen, setIsOpen] = useState(false)\n  const dropdownRef = useRef<HTMLDivElement>(null)\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false)\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => document.removeEventListener('mousedown', handleClickOutside)\n  }, [])\n\n  // Close dropdown on escape key\n  useEffect(() => {\n    const handleEscape = (event: KeyboardEvent) => {\n      if (event.key === 'Escape') {\n        setIsOpen(false)\n      }\n    }\n\n    document.addEventListener('keydown', handleEscape)\n    return () => document.removeEventListener('keydown', handleEscape)\n  }, [])\n\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(word => word.charAt(0))\n      .join('')\n      .toUpperCase()\n      .slice(0, 2)\n  }\n\n  const handleLogout = () => {\n    setIsOpen(false)\n    onLogout()\n  }\n\n  return (\n    <div className=\"relative\" ref={dropdownRef}>\n      {/* Profile Button */}\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"flex items-center space-x-3 p-2 rounded-lg hover:bg-secondary-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2\"\n      >\n        {/* Avatar */}\n        <div className=\"w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center text-white text-sm font-medium\">\n          {getInitials(user.name)}\n        </div>\n        \n        {/* User Info */}\n        <div className=\"hidden md:block text-left\">\n          <p className=\"text-sm font-medium text-secondary-900\">{user.name}</p>\n          <p className=\"text-xs text-secondary-600\">{user.email}</p>\n        </div>\n        \n        {/* Dropdown Arrow */}\n        <ChevronDown className={`w-4 h-4 text-secondary-600 transition-transform duration-200 ${\n          isOpen ? 'transform rotate-180' : ''\n        }`} />\n      </button>\n\n      {/* Dropdown Menu */}\n      {isOpen && (\n        <div className=\"absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-secondary-200 py-2 z-50\">\n          {/* User Info Header */}\n          <div className=\"px-4 py-3 border-b border-secondary-200\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center text-white font-medium\">\n                {getInitials(user.name)}\n              </div>\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm font-medium text-secondary-900 truncate\">\n                  {user.name}\n                </p>\n                <p className=\"text-xs text-secondary-600 truncate\">\n                  {user.email}\n                </p>\n                {user.username && (\n                  <p className=\"text-xs text-secondary-500 truncate\">\n                    @{user.username}\n                  </p>\n                )}\n              </div>\n            </div>\n            \n            {/* User Roles/Groups */}\n            {(user.roles && user.roles.length > 0) || (user.groups && user.groups.length > 0) ? (\n              <div className=\"mt-2 flex flex-wrap gap-1\">\n                {user.roles?.map((role) => (\n                  <span\n                    key={role}\n                    className=\"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-primary-100 text-primary-800\"\n                  >\n                    {role}\n                  </span>\n                ))}\n                {user.groups?.map((group) => (\n                  <span\n                    key={group}\n                    className=\"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-secondary-100 text-secondary-800\"\n                  >\n                    {group}\n                  </span>\n                ))}\n              </div>\n            ) : null}\n          </div>\n\n          {/* Menu Items */}\n          <div className=\"py-1\">\n            <button\n              onClick={() => {\n                setIsOpen(false)\n                // Handle profile view - you could navigate to a profile page\n                console.log('View profile clicked')\n              }}\n              className=\"flex items-center w-full px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-100 transition-colors duration-200\"\n            >\n              <User className=\"w-4 h-4 mr-3\" />\n              View Profile\n            </button>\n\n            <button\n              onClick={() => {\n                setIsOpen(false)\n                // Handle activity view - you could navigate to an activity page\n                console.log('View activity clicked')\n              }}\n              className=\"flex items-center w-full px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-100 transition-colors duration-200\"\n            >\n              <Activity className=\"w-4 h-4 mr-3\" />\n              Activity\n            </button>\n\n            <button\n              onClick={() => {\n                setIsOpen(false)\n                // Handle settings - you could navigate to a settings page\n                console.log('Settings clicked')\n              }}\n              className=\"flex items-center w-full px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-100 transition-colors duration-200\"\n            >\n              <Settings className=\"w-4 h-4 mr-3\" />\n              Settings\n            </button>\n          </div>\n\n          {/* Logout */}\n          <div className=\"border-t border-secondary-200 py-1\">\n            <button\n              onClick={handleLogout}\n              className=\"flex items-center w-full px-4 py-2 text-sm text-danger-700 hover:bg-danger-50 transition-colors duration-200\"\n            >\n              <LogOut className=\"w-4 h-4 mr-3\" />\n              Sign Out\n            </button>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAWe,SAAS,YAAY,EAAE,IAAI,EAAE,QAAQ,EAAoB;;IACtE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;4DAAqB,CAAC;oBAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAC9E,UAAU;oBACZ;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;yCAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;;QACzD;gCAAG,EAAE;IAEL,+BAA+B;IAC/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;sDAAe,CAAC;oBACpB,IAAI,MAAM,GAAG,KAAK,UAAU;wBAC1B,UAAU;oBACZ;gBACF;;YAEA,SAAS,gBAAgB,CAAC,WAAW;YACrC;yCAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;;QACvD;gCAAG,EAAE;IAEL,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,IACxB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,MAAM,eAAe;QACnB,UAAU;QACV;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;QAAW,KAAK;;0BAE7B,6LAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;kCAGV,6LAAC;wBAAI,WAAU;kCACZ,YAAY,KAAK,IAAI;;;;;;kCAIxB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAA0C,KAAK,IAAI;;;;;;0CAChE,6LAAC;gCAAE,WAAU;0CAA8B,KAAK,KAAK;;;;;;;;;;;;kCAIvD,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAW,CAAC,6DAA6D,EACpF,SAAS,yBAAyB,IAClC;;;;;;;;;;;;YAIH,wBACC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,YAAY,KAAK,IAAI;;;;;;kDAExB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DACV,KAAK,IAAI;;;;;;0DAEZ,6LAAC;gDAAE,WAAU;0DACV,KAAK,KAAK;;;;;;4CAEZ,KAAK,QAAQ,kBACZ,6LAAC;gDAAE,WAAU;;oDAAsC;oDAC/C,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;4BAOrB,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,KAAO,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,MAAM,GAAG,kBAC7E,6LAAC;gCAAI,WAAU;;oCACZ,KAAK,KAAK,EAAE,IAAI,CAAC,qBAChB,6LAAC;4CAEC,WAAU;sDAET;2CAHI;;;;;oCAMR,KAAK,MAAM,EAAE,IAAI,CAAC,sBACjB,6LAAC;4CAEC,WAAU;sDAET;2CAHI;;;;;;;;;;uCAOT;;;;;;;kCAIN,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;oCACP,UAAU;oCACV,6DAA6D;oCAC7D,QAAQ,GAAG,CAAC;gCACd;gCACA,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAInC,6LAAC;gCACC,SAAS;oCACP,UAAU;oCACV,gEAAgE;oCAChE,QAAQ,GAAG,CAAC;gCACd;gCACA,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAIvC,6LAAC;gCACC,SAAS;oCACP,UAAU;oCACV,0DAA0D;oCAC1D,QAAQ,GAAG,CAAC;gCACd;gCACA,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAMzC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,6LAAC,6MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AAQjD;GAtKwB;KAAA", "debugId": null}}, {"offset": {"line": 2152, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Choreo/frontend/src/lib/api.ts"], "sourcesContent": ["/**\n * API client for communicating with the Task Management backend\n * Handles authentication, error handling, and API calls\n */\n\nimport { \n  Task, \n  CreateTaskRequest, \n  UpdateTaskRequest, \n  TaskFilters, \n  TaskListResponse, \n  TaskStats,\n  User,\n  UserPreferences,\n  UserActivity,\n  ApiError \n} from '@/types'\nimport { handleSessionExpiry } from './auth'\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '/choreo-apis'\n\n/**\n * Custom error class for API errors\n */\nexport class ApiClientError extends Error {\n  constructor(\n    message: string,\n    public status: number,\n    public response?: any\n  ) {\n    super(message)\n    this.name = 'ApiClientError'\n  }\n}\n\n/**\n * API client class for making authenticated requests\n */\nclass ApiClient {\n  private baseUrl: string\n\n  constructor(baseUrl: string = API_BASE_URL) {\n    this.baseUrl = baseUrl\n  }\n\n  /**\n   * Make an authenticated API request\n   */\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<T> {\n    const url = `${this.baseUrl}${endpoint}`\n    \n    const config: RequestInit = {\n      credentials: 'include', // Include cookies for Choreo auth\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json',\n        ...options.headers,\n      },\n      ...options,\n    }\n\n    try {\n      const response = await fetch(url, config)\n      \n      // Handle authentication errors\n      if (response.status === 401) {\n        handleSessionExpiry()\n        throw new ApiClientError('Authentication required', 401)\n      }\n      \n      // Parse response\n      const data = await response.json()\n      \n      if (!response.ok) {\n        throw new ApiClientError(\n          data.message || `HTTP ${response.status}`,\n          response.status,\n          data\n        )\n      }\n      \n      return data\n    } catch (error) {\n      if (error instanceof ApiClientError) {\n        throw error\n      }\n      \n      // Network or other errors\n      console.error('API request failed:', error)\n      throw new ApiClientError(\n        'Network error or server unavailable',\n        0,\n        error\n      )\n    }\n  }\n\n  // Task API methods\n  async getTasks(filters?: TaskFilters): Promise<TaskListResponse> {\n    const params = new URLSearchParams()\n    \n    if (filters?.status) params.append('status', filters.status)\n    if (filters?.priority) params.append('priority', filters.priority)\n    if (filters?.sortBy) params.append('sortBy', filters.sortBy)\n    if (filters?.sortOrder) params.append('sortOrder', filters.sortOrder)\n    \n    const queryString = params.toString()\n    const endpoint = `/tasks${queryString ? `?${queryString}` : ''}`\n    \n    return this.request<TaskListResponse>(endpoint)\n  }\n\n  async getTask(id: string): Promise<{ task: Task }> {\n    return this.request<{ task: Task }>(`/tasks/${id}`)\n  }\n\n  async createTask(data: CreateTaskRequest): Promise<{ task: Task; message: string }> {\n    return this.request<{ task: Task; message: string }>('/tasks', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    })\n  }\n\n  async updateTask(id: string, data: UpdateTaskRequest): Promise<{ task: Task; message: string }> {\n    return this.request<{ task: Task; message: string }>(`/tasks/${id}`, {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    })\n  }\n\n  async deleteTask(id: string): Promise<{ message: string; taskId: string }> {\n    return this.request<{ message: string; taskId: string }>(`/tasks/${id}`, {\n      method: 'DELETE',\n    })\n  }\n\n  async getTaskStats(): Promise<{ stats: TaskStats }> {\n    return this.request<{ stats: TaskStats }>('/tasks/stats')\n  }\n\n  // User API methods\n  async getUserProfile(): Promise<{ profile: User }> {\n    return this.request<{ profile: User }>('/user/profile')\n  }\n\n  async getUserPreferences(): Promise<{ preferences: UserPreferences }> {\n    return this.request<{ preferences: UserPreferences }>('/user/preferences')\n  }\n\n  async updateUserPreferences(preferences: Partial<UserPreferences>): Promise<{ preferences: UserPreferences }> {\n    return this.request<{ preferences: UserPreferences }>('/user/preferences', {\n      method: 'PUT',\n      body: JSON.stringify(preferences),\n    })\n  }\n\n  async getUserActivity(): Promise<{ activity: UserActivity }> {\n    return this.request<{ activity: UserActivity }>('/user/activity')\n  }\n\n  async logoutUser(): Promise<{ message: string }> {\n    return this.request<{ message: string }>('/user/logout', {\n      method: 'POST',\n    })\n  }\n\n  async getUserPermissions(): Promise<{ permissions: any }> {\n    return this.request<{ permissions: any }>('/user/permissions')\n  }\n}\n\n// Create and export API client instance\nexport const apiClient = new ApiClient()\n\n// Convenience functions for common operations\nexport const taskApi = {\n  getAll: (filters?: TaskFilters) => apiClient.getTasks(filters),\n  getById: (id: string) => apiClient.getTask(id),\n  create: (data: CreateTaskRequest) => apiClient.createTask(data),\n  update: (id: string, data: UpdateTaskRequest) => apiClient.updateTask(id, data),\n  delete: (id: string) => apiClient.deleteTask(id),\n  getStats: () => apiClient.getTaskStats(),\n}\n\nexport const userApi = {\n  getProfile: () => apiClient.getUserProfile(),\n  getPreferences: () => apiClient.getUserPreferences(),\n  updatePreferences: (preferences: Partial<UserPreferences>) => \n    apiClient.updateUserPreferences(preferences),\n  getActivity: () => apiClient.getUserActivity(),\n  logout: () => apiClient.logoutUser(),\n  getPermissions: () => apiClient.getUserPermissions(),\n}\n\n// Error handling utilities\nexport const isApiError = (error: any): error is ApiClientError => {\n  return error instanceof ApiClientError\n}\n\nexport const getErrorMessage = (error: any): string => {\n  if (isApiError(error)) {\n    return error.message\n  }\n  \n  if (error instanceof Error) {\n    return error.message\n  }\n  \n  return 'An unexpected error occurred'\n}\n\nexport const isNetworkError = (error: any): boolean => {\n  return isApiError(error) && error.status === 0\n}\n\nexport const isAuthError = (error: any): boolean => {\n  return isApiError(error) && error.status === 401\n}\n\nexport const isValidationError = (error: any): boolean => {\n  return isApiError(error) && error.status === 400\n}\n\nexport const isNotFoundError = (error: any): boolean => {\n  return isApiError(error) && error.status === 404\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;AAgBoB;AAFrB;;AAEA,MAAM,eAAe,oDAAwC;AAKtD,MAAM,uBAAuB;;;IAClC,YACE,OAAe,EACf,AAAO,MAAc,EACrB,AAAO,QAAc,CACrB;QACA,KAAK,CAAC,eAHC,SAAA,aACA,WAAA;QAGP,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEA;;CAEC,GACD,MAAM;IACI,QAAe;IAEvB,YAAY,UAAkB,YAAY,CAAE;QAC1C,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA;;GAEC,GACD,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACb;QACZ,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;QAExC,MAAM,SAAsB;YAC1B,aAAa;YACb,SAAS;gBACP,gBAAgB;gBAChB,UAAU;gBACV,GAAG,QAAQ,OAAO;YACpB;YACA,GAAG,OAAO;QACZ;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,KAAK;YAElC,+BAA+B;YAC/B,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B,CAAA,GAAA,qHAAA,CAAA,sBAAmB,AAAD;gBAClB,MAAM,IAAI,eAAe,2BAA2B;YACtD;YAEA,iBAAiB;YACjB,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,eACR,KAAK,OAAO,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,EAAE,EACzC,SAAS,MAAM,EACf;YAEJ;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,gBAAgB;gBACnC,MAAM;YACR;YAEA,0BAA0B;YAC1B,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM,IAAI,eACR,uCACA,GACA;QAEJ;IACF;IAEA,mBAAmB;IACnB,MAAM,SAAS,OAAqB,EAA6B;QAC/D,MAAM,SAAS,IAAI;QAEnB,IAAI,SAAS,QAAQ,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;QAC3D,IAAI,SAAS,UAAU,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ;QACjE,IAAI,SAAS,QAAQ,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;QAC3D,IAAI,SAAS,WAAW,OAAO,MAAM,CAAC,aAAa,QAAQ,SAAS;QAEpE,MAAM,cAAc,OAAO,QAAQ;QACnC,MAAM,WAAW,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAEhE,OAAO,IAAI,CAAC,OAAO,CAAmB;IACxC;IAEA,MAAM,QAAQ,EAAU,EAA2B;QACjD,OAAO,IAAI,CAAC,OAAO,CAAiB,CAAC,OAAO,EAAE,IAAI;IACpD;IAEA,MAAM,WAAW,IAAuB,EAA4C;QAClF,OAAO,IAAI,CAAC,OAAO,CAAkC,UAAU;YAC7D,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAE,IAAuB,EAA4C;QAC9F,OAAO,IAAI,CAAC,OAAO,CAAkC,CAAC,OAAO,EAAE,IAAI,EAAE;YACnE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,WAAW,EAAU,EAAgD;QACzE,OAAO,IAAI,CAAC,OAAO,CAAsC,CAAC,OAAO,EAAE,IAAI,EAAE;YACvE,QAAQ;QACV;IACF;IAEA,MAAM,eAA8C;QAClD,OAAO,IAAI,CAAC,OAAO,CAAuB;IAC5C;IAEA,mBAAmB;IACnB,MAAM,iBAA6C;QACjD,OAAO,IAAI,CAAC,OAAO,CAAoB;IACzC;IAEA,MAAM,qBAAgE;QACpE,OAAO,IAAI,CAAC,OAAO,CAAmC;IACxD;IAEA,MAAM,sBAAsB,WAAqC,EAA6C;QAC5G,OAAO,IAAI,CAAC,OAAO,CAAmC,qBAAqB;YACzE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,kBAAuD;QAC3D,OAAO,IAAI,CAAC,OAAO,CAA6B;IAClD;IAEA,MAAM,aAA2C;QAC/C,OAAO,IAAI,CAAC,OAAO,CAAsB,gBAAgB;YACvD,QAAQ;QACV;IACF;IAEA,MAAM,qBAAoD;QACxD,OAAO,IAAI,CAAC,OAAO,CAAuB;IAC5C;AACF;AAGO,MAAM,YAAY,IAAI;AAGtB,MAAM,UAAU;IACrB,QAAQ,CAAC,UAA0B,UAAU,QAAQ,CAAC;IACtD,SAAS,CAAC,KAAe,UAAU,OAAO,CAAC;IAC3C,QAAQ,CAAC,OAA4B,UAAU,UAAU,CAAC;IAC1D,QAAQ,CAAC,IAAY,OAA4B,UAAU,UAAU,CAAC,IAAI;IAC1E,QAAQ,CAAC,KAAe,UAAU,UAAU,CAAC;IAC7C,UAAU,IAAM,UAAU,YAAY;AACxC;AAEO,MAAM,UAAU;IACrB,YAAY,IAAM,UAAU,cAAc;IAC1C,gBAAgB,IAAM,UAAU,kBAAkB;IAClD,mBAAmB,CAAC,cAClB,UAAU,qBAAqB,CAAC;IAClC,aAAa,IAAM,UAAU,eAAe;IAC5C,QAAQ,IAAM,UAAU,UAAU;IAClC,gBAAgB,IAAM,UAAU,kBAAkB;AACpD;AAGO,MAAM,aAAa,CAAC;IACzB,OAAO,iBAAiB;AAC1B;AAEO,MAAM,kBAAkB,CAAC;IAC9B,IAAI,WAAW,QAAQ;QACrB,OAAO,MAAM,OAAO;IACtB;IAEA,IAAI,iBAAiB,OAAO;QAC1B,OAAO,MAAM,OAAO;IACtB;IAEA,OAAO;AACT;AAEO,MAAM,iBAAiB,CAAC;IAC7B,OAAO,WAAW,UAAU,MAAM,MAAM,KAAK;AAC/C;AAEO,MAAM,cAAc,CAAC;IAC1B,OAAO,WAAW,UAAU,MAAM,MAAM,KAAK;AAC/C;AAEO,MAAM,oBAAoB,CAAC;IAChC,OAAO,WAAW,UAAU,MAAM,MAAM,KAAK;AAC/C;AAEO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,WAAW,UAAU,MAAM,MAAM,KAAK;AAC/C", "debugId": null}}, {"offset": {"line": 2330, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Choreo/frontend/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client'\n\nimport ErrorMessage from '@/components/ErrorMessage'\nimport FilterBar from '@/components/FilterBar'\nimport LoadingSpinner from '@/components/LoadingSpinner'\nimport TaskForm from '@/components/TaskForm'\nimport TaskList from '@/components/TaskList'\nimport { ToastContainer, useToast } from '@/components/Toast'\nimport UserProfile from '@/components/UserProfile'\nimport { getErrorMessage, taskApi } from '@/lib/api'\nimport { authService } from '@/lib/auth'\nimport { Task, TaskFilters, TaskStats, User } from '@/types'\nimport { AlertCircle, BarChart3, CheckCircle, Clock, Plus, Search } from 'lucide-react'\nimport { useEffect, useState } from 'react'\n\nexport default function DashboardPage() {\n  // State management\n  const [user, setUser] = useState<User | null>(null)\n  const { toasts, removeToast, success, error: showError } = useToast()\n  const [tasks, setTasks] = useState<Task[]>([])\n  const [taskStats, setTaskStats] = useState<TaskStats | null>(null)\n  const [filters, setFilters] = useState<TaskFilters>({})\n  const [isLoading, setIsLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n  const [showTaskForm, setShowTaskForm] = useState(false)\n  const [editingTask, setEditingTask] = useState<Task | null>(null)\n  const [searchQuery, setSearchQuery] = useState('')\n\n  // Authentication and initial data loading\n  useEffect(() => {\n    initializeDashboard()\n  }, []) // eslint-disable-line react-hooks/exhaustive-deps\n\n  // Load tasks when filters change\n  useEffect(() => {\n    if (user) {\n      loadTasks()\n    }\n  }, [filters, user]) // eslint-disable-line react-hooks/exhaustive-deps\n\n  const initializeDashboard = async () => {\n    try {\n      setIsLoading(true)\n      setError(null)\n\n      // Check authentication status\n      const authResult = await authService.checkAuthStatus()\n      \n      if (!authResult.isAuthenticated) {\n        // Redirect to login if not authenticated\n        authService.login('/dashboard')\n        return\n      }\n\n      setUser(authResult.user)\n      \n      // Load initial data\n      await Promise.all([\n        loadTasks(),\n        loadTaskStats()\n      ])\n    } catch (err) {\n      console.error('Dashboard initialization error:', err)\n      setError(getErrorMessage(err))\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const loadTasks = async () => {\n    try {\n      const response = await taskApi.getAll(filters)\n      setTasks(response.tasks)\n    } catch (err) {\n      console.error('Error loading tasks:', err)\n      setError(getErrorMessage(err))\n    }\n  }\n\n  const loadTaskStats = async () => {\n    try {\n      const response = await taskApi.getStats()\n      setTaskStats(response.stats)\n    } catch (err) {\n      console.error('Error loading task stats:', err)\n      // Don't set error for stats failure, just log it\n    }\n  }\n\n  const handleCreateTask = async (taskData: any) => {\n    try {\n      const response = await taskApi.create(taskData)\n      setTasks(prev => [response.task, ...prev])\n      setShowTaskForm(false)\n      await loadTaskStats() // Refresh stats\n      success('Task created', 'Your new task has been created successfully')\n    } catch (err) {\n      console.error('Error creating task:', err)\n      showError('Failed to create task', getErrorMessage(err))\n    }\n  }\n\n  const handleUpdateTask = async (taskData: any) => {\n    if (!editingTask) return\n\n    try {\n      const response = await taskApi.update(editingTask.id, taskData)\n      setTasks(prev => prev.map(task =>\n        task.id === editingTask.id ? response.task : task\n      ))\n      setEditingTask(null)\n      await loadTaskStats() // Refresh stats\n      success('Task updated', 'Your task has been updated successfully')\n    } catch (err) {\n      console.error('Error updating task:', err)\n      showError('Failed to update task', getErrorMessage(err))\n    }\n  }\n\n  const handleDeleteTask = async (taskId: string) => {\n    try {\n      await taskApi.delete(taskId)\n      setTasks(prev => prev.filter(task => task.id !== taskId))\n      await loadTaskStats() // Refresh stats\n    } catch (err) {\n      console.error('Error deleting task:', err)\n      setError(getErrorMessage(err))\n    }\n  }\n\n  const handleTaskEdit = (task: Task) => {\n    setEditingTask(task)\n    setShowTaskForm(true)\n  }\n\n  const handleFiltersChange = (newFilters: TaskFilters) => {\n    setFilters(newFilters)\n  }\n\n  const handleLogout = () => {\n    authService.logout()\n  }\n\n  // Filter tasks by search query\n  const filteredTasks = tasks.filter(task =>\n    task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\n    task.description.toLowerCase().includes(searchQuery.toLowerCase())\n  )\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-secondary-50\">\n        <LoadingSpinner size=\"large\" />\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-secondary-50\">\n      {/* Toast Notifications */}\n      <ToastContainer toasts={toasts} onClose={removeToast} />\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b border-secondary-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center\">\n                <CheckCircle className=\"w-5 h-5 text-white\" />\n              </div>\n              <h1 className=\"text-xl font-bold text-secondary-900\">\n                Task Management\n              </h1>\n            </div>\n            \n            <div className=\"flex items-center space-x-4\">\n              <button\n                onClick={() => setShowTaskForm(true)}\n                className=\"btn-primary flex items-center space-x-2\"\n              >\n                <Plus className=\"w-4 h-4\" />\n                <span>New Task</span>\n              </button>\n              \n              {user && (\n                <UserProfile user={user} onLogout={handleLogout} />\n              )}\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {error && (\n          <div className=\"mb-6\">\n            <ErrorMessage \n              message={error} \n              onDismiss={() => setError(null)} \n            />\n          </div>\n        )}\n\n        {/* Stats Cards */}\n        {taskStats && (\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n            <div className=\"card p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-secondary-600\">Total Tasks</p>\n                  <p className=\"text-2xl font-bold text-secondary-900\">{taskStats.total}</p>\n                </div>\n                <BarChart3 className=\"w-8 h-8 text-primary-600\" />\n              </div>\n            </div>\n            \n            <div className=\"card p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-secondary-600\">In Progress</p>\n                  <p className=\"text-2xl font-bold text-warning-600\">{taskStats.byStatus['in-progress']}</p>\n                </div>\n                <Clock className=\"w-8 h-8 text-warning-600\" />\n              </div>\n            </div>\n            \n            <div className=\"card p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-secondary-600\">Completed</p>\n                  <p className=\"text-2xl font-bold text-success-600\">{taskStats.byStatus.completed}</p>\n                </div>\n                <CheckCircle className=\"w-8 h-8 text-success-600\" />\n              </div>\n            </div>\n            \n            <div className=\"card p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-secondary-600\">Overdue</p>\n                  <p className=\"text-2xl font-bold text-danger-600\">{taskStats.overdue}</p>\n                </div>\n                <AlertCircle className=\"w-8 h-8 text-danger-600\" />\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Filters and Search */}\n        <div className=\"card mb-6\">\n          <div className=\"card-body\">\n            <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0\">\n              <div className=\"flex-1 max-w-md\">\n                <div className=\"relative\">\n                  <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-secondary-400\" />\n                  <input\n                    type=\"text\"\n                    placeholder=\"Search tasks...\"\n                    value={searchQuery}\n                    onChange={(e) => setSearchQuery(e.target.value)}\n                    className=\"form-input pl-10\"\n                  />\n                </div>\n              </div>\n              \n              {taskStats && (\n                <FilterBar\n                  filters={filters}\n                  onFiltersChange={handleFiltersChange}\n                  taskStats={taskStats}\n                />\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Task List */}\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h2 className=\"text-lg font-semibold text-secondary-900\">\n              Your Tasks ({filteredTasks.length})\n            </h2>\n          </div>\n          <div className=\"card-body p-0\">\n            <TaskList\n              tasks={filteredTasks}\n              onEdit={handleTaskEdit}\n              onDelete={handleDeleteTask}\n              onUpdate={loadTasks}\n            />\n          </div>\n        </div>\n      </main>\n\n      {/* Task Form Modal */}\n      {showTaskForm && (\n        <TaskForm\n          task={editingTask}\n          onSubmit={editingTask ? handleUpdateTask : handleCreateTask}\n          onCancel={() => {\n            setShowTaskForm(false)\n            setEditingTask(null)\n          }}\n        />\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAbA;;;;;;;;;;;;AAee,SAAS;;IACtB,mBAAmB;IACnB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,SAAS,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,WAAQ,AAAD;IAClE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,CAAC;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,0CAA0C;IAC1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG,EAAE,EAAE,kDAAkD;;IAEzD,iCAAiC;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,MAAM;gBACR;YACF;QACF;kCAAG;QAAC;QAAS;KAAK,EAAE,kDAAkD;;IAEtE,MAAM,sBAAsB;QAC1B,IAAI;YACF,aAAa;YACb,SAAS;YAET,8BAA8B;YAC9B,MAAM,aAAa,MAAM,qHAAA,CAAA,cAAW,CAAC,eAAe;YAEpD,IAAI,CAAC,WAAW,eAAe,EAAE;gBAC/B,yCAAyC;gBACzC,qHAAA,CAAA,cAAW,CAAC,KAAK,CAAC;gBAClB;YACF;YAEA,QAAQ,WAAW,IAAI;YAEvB,oBAAoB;YACpB,MAAM,QAAQ,GAAG,CAAC;gBAChB;gBACA;aACD;QACH,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,mCAAmC;YACjD,SAAS,CAAA,GAAA,oHAAA,CAAA,kBAAe,AAAD,EAAE;QAC3B,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,MAAM,CAAC;YACtC,SAAS,SAAS,KAAK;QACzB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,wBAAwB;YACtC,SAAS,CAAA,GAAA,oHAAA,CAAA,kBAAe,AAAD,EAAE;QAC3B;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,QAAQ;YACvC,aAAa,SAAS,KAAK;QAC7B,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,iDAAiD;QACnD;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,MAAM,CAAC;YACtC,SAAS,CAAA,OAAQ;oBAAC,SAAS,IAAI;uBAAK;iBAAK;YACzC,gBAAgB;YAChB,MAAM,gBAAgB,gBAAgB;;YACtC,QAAQ,gBAAgB;QAC1B,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,wBAAwB;YACtC,UAAU,yBAAyB,CAAA,GAAA,oHAAA,CAAA,kBAAe,AAAD,EAAE;QACrD;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,aAAa;QAElB,IAAI;YACF,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,MAAM,CAAC,YAAY,EAAE,EAAE;YACtD,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OACxB,KAAK,EAAE,KAAK,YAAY,EAAE,GAAG,SAAS,IAAI,GAAG;YAE/C,eAAe;YACf,MAAM,gBAAgB,gBAAgB;;YACtC,QAAQ,gBAAgB;QAC1B,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,wBAAwB;YACtC,UAAU,yBAAyB,CAAA,GAAA,oHAAA,CAAA,kBAAe,AAAD,EAAE;QACrD;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,oHAAA,CAAA,UAAO,CAAC,MAAM,CAAC;YACrB,SAAS,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YACjD,MAAM,gBAAgB,gBAAgB;;QACxC,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,wBAAwB;YACtC,SAAS,CAAA,GAAA,oHAAA,CAAA,kBAAe,AAAD,EAAE;QAC3B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,eAAe;QACf,gBAAgB;IAClB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,WAAW;IACb;IAEA,MAAM,eAAe;QACnB,qHAAA,CAAA,cAAW,CAAC,MAAM;IACpB;IAEA,+BAA+B;IAC/B,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,OACjC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACzD,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;IAGjE,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,uIAAA,CAAA,UAAc;gBAAC,MAAK;;;;;;;;;;;IAG3B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,8HAAA,CAAA,iBAAc;gBAAC,QAAQ;gBAAQ,SAAS;;;;;;0BAEzC,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;kDAEzB,6LAAC;wCAAG,WAAU;kDAAuC;;;;;;;;;;;;0CAKvD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,gBAAgB;wCAC/B,WAAU;;0DAEV,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;0DAAK;;;;;;;;;;;;oCAGP,sBACC,6LAAC,oIAAA,CAAA,UAAW;wCAAC,MAAM;wCAAM,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO7C,6LAAC;gBAAK,WAAU;;oBACb,uBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qIAAA,CAAA,UAAY;4BACX,SAAS;4BACT,WAAW,IAAM,SAAS;;;;;;;;;;;oBAM/B,2BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAyC;;;;;;8DACtD,6LAAC;oDAAE,WAAU;8DAAyC,UAAU,KAAK;;;;;;;;;;;;sDAEvE,6LAAC,uNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIzB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAyC;;;;;;8DACtD,6LAAC;oDAAE,WAAU;8DAAuC,UAAU,QAAQ,CAAC,cAAc;;;;;;;;;;;;sDAEvF,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAIrB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAyC;;;;;;8DACtD,6LAAC;oDAAE,WAAU;8DAAuC,UAAU,QAAQ,CAAC,SAAS;;;;;;;;;;;;sDAElF,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAI3B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAyC;;;;;;8DACtD,6LAAC;oDAAE,WAAU;8DAAsC,UAAU,OAAO;;;;;;;;;;;;sDAEtE,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAO/B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC9C,WAAU;;;;;;;;;;;;;;;;;oCAKf,2BACC,6LAAC,kIAAA,CAAA,UAAS;wCACR,SAAS;wCACT,iBAAiB;wCACjB,WAAW;;;;;;;;;;;;;;;;;;;;;;kCAQrB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;;wCAA2C;wCAC1C,cAAc,MAAM;wCAAC;;;;;;;;;;;;0CAGtC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,iIAAA,CAAA,UAAQ;oCACP,OAAO;oCACP,QAAQ;oCACR,UAAU;oCACV,UAAU;;;;;;;;;;;;;;;;;;;;;;;YAOjB,8BACC,6LAAC,iIAAA,CAAA,UAAQ;gBACP,MAAM;gBACN,UAAU,cAAc,mBAAmB;gBAC3C,UAAU;oBACR,gBAAgB;oBAChB,eAAe;gBACjB;;;;;;;;;;;;AAKV;GAlSwB;;QAGqC,8HAAA,CAAA,WAAQ;;;KAH7C", "debugId": null}}]}